#!/usr/bin/env python3
"""
Master-Know 系统端到端集成测试脚本
测试完整的文档处理流程：上传 → 切片 → 向量化 → 索引 → 检索 → LLM 对话
"""

import asyncio
import httpx
import requests
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

class SystemIntegrationTester:
    """系统集成测试器"""

    def __init__(self, base_url: str = "http://api.localhost.tiangolo.com"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(
            timeout=30.0,
            follow_redirects=True,
            verify=False
        )
        self.access_token: Optional[str] = None
        self.test_document_id: Optional[str] = None
        self.test_results = {}
        # 使用主机可访问的地址
        self.base_urls = {
            "manticore_http": "http://localhost:9308",
            "redis": "redis://localhost:6379",
            "postgres": "postgresql://master_know_user:changethis@localhost:5432/master_know"
        }
        
    def test_manticore_connection(self) -> bool:
        """测试 Manticore Search 连接"""
        print("🔍 测试 Manticore Search 连接...")

        try:
            # 简单的端口连接测试
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 9308))
            sock.close()

            if result == 0:
                print("   ✅ Manticore HTTP 端口连接成功")

                # 尝试测试 MySQL 端口
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                mysql_result = sock.connect_ex(('localhost', 9306))
                sock.close()

                if mysql_result == 0:
                    print("   ✅ Manticore MySQL 端口连接成功")
                    return True
                else:
                    print("   ⚠️  Manticore MySQL 端口连接失败")
                    return True  # HTTP 端口正常就算成功
            else:
                print("   ❌ Manticore 端口连接失败")
                return False

        except Exception as e:
            print(f"   ❌ Manticore 连接测试失败: {e}")
            return False
    
    def test_redis_connection(self) -> bool:
        """测试 Redis 连接"""
        print("📦 测试 Redis 连接...")
        
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            
            # 测试基本操作
            r.set('test_key', 'test_value')
            value = r.get('test_key')
            
            if value and value.decode() == 'test_value':
                print("   ✅ Redis 连接和基本操作成功")
                r.delete('test_key')
                return True
            else:
                print("   ❌ Redis 基本操作失败")
                return False
                
        except ImportError:
            print("   ⚠️  Redis 库未安装，跳过测试")
            return False
        except Exception as e:
            print(f"   ❌ Redis 连接测试失败: {e}")
            return False
    
    def test_postgres_connection(self) -> bool:
        """测试 PostgreSQL 连接"""
        print("🐘 测试 PostgreSQL 连接...")
        
        try:
            import psycopg2
            
            conn = psycopg2.connect(
                host="localhost",
                port=5432,
                database="master_know",
                user="master_know_user",
                password="changethis"
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            
            if version:
                print(f"   ✅ PostgreSQL 连接成功: {version[0][:50]}...")
                cursor.close()
                conn.close()
                return True
            else:
                print("   ❌ PostgreSQL 查询失败")
                return False
                
        except ImportError:
            print("   ⚠️  psycopg2 库未安装，跳过测试")
            return False
        except Exception as e:
            print(f"   ❌ PostgreSQL 连接测试失败: {e}")
            return False
    
    def test_text_splitter_engine(self) -> bool:
        """测试文本分割引擎"""
        print("✂️  测试文本分割引擎...")
        
        try:
            # 尝试导入 engines 模块
            import sys
            sys.path.insert(0, str(Path(__file__).parent))
            
            from engines.text_splitter import TextSplitterEngine, TextSplitterConfig

            # 创建测试文本
            test_text = """
            这是一个测试文档。它包含多个段落和句子。

            第二段包含更多的内容，用于测试文本分割功能。
            我们希望能够将长文本分割成合适大小的块。

            第三段继续测试分割功能的准确性和效率。
            """

            # 初始化分割器
            config = TextSplitterConfig(default_max_tokens=100)
            engine = TextSplitterEngine(config)
            chunks = engine.split_text(test_text, strategy="token_based")
            
            if chunks and len(chunks) > 0:
                print(f"   ✅ 文本分割成功，生成 {len(chunks)} 个块")
                for i, chunk in enumerate(chunks[:2]):  # 显示前两个块
                    print(f"      块 {i+1}: {chunk.content[:50]}...")
                return True
            else:
                print("   ❌ 文本分割失败，未生成块")
                return False
                
        except ImportError as e:
            print(f"   ❌ 文本分割引擎导入失败: {e}")
            return False
        except Exception as e:
            print(f"   ❌ 文本分割测试失败: {e}")
            return False
    
    def test_embedding_api(self) -> bool:
        """测试嵌入 API 连接"""
        print("🧠 测试嵌入 API 连接...")
        
        try:
            import openai
            
            # 使用环境变量中的配置
            client = openai.OpenAI(
                api_key="sk-ePLBAF0SMDPSrqZ9VS1RbYSZaphIOrHhFQHnlfjlWjNt8k4Z",
                base_url="https://ai98.vip/v1"
            )
            
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input="测试文本嵌入功能"
            )
            
            if response.data and len(response.data) > 0:
                embedding_dim = len(response.data[0].embedding)
                print(f"   ✅ 嵌入 API 连接成功，维度: {embedding_dim}")
                return True
            else:
                print("   ❌ 嵌入 API 响应异常")
                return False
                
        except ImportError:
            print("   ⚠️  OpenAI 库未安装，跳过测试")
            return False
        except Exception as e:
            print(f"   ❌ 嵌入 API 测试失败: {e}")
            return False
    
    def run_integration_tests(self) -> bool:
        """运行集成测试"""
        print("🚀 开始系统集成测试...")
        print("=" * 60)
        
        test_results = {}
        
        # 1. 测试基础服务连接
        test_results["manticore"] = self.test_manticore_connection()
        test_results["redis"] = self.test_redis_connection()
        test_results["postgres"] = self.test_postgres_connection()
        
        # 2. 测试核心功能模块
        test_results["text_splitter"] = self.test_text_splitter_engine()
        test_results["embedding_api"] = self.test_embedding_api()
        
        # 统计结果
        passed = sum(1 for result in test_results.values() if result)
        total = len(test_results)
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        # 显示详细结果
        print("\n📋 详细结果:")
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        if passed == total:
            print("\n🎉 所有集成测试通过！系统基础功能正常。")
        else:
            print(f"\n⚠️  {total - passed}个测试失败，请检查相关服务。")
        
        return passed == total

    async def test_backend_health(self) -> bool:
        """测试 Backend API 健康检查"""
        print("🏥 测试 Backend API 健康检查...")
        print(f"   🔗 测试 URL: {self.base_url}/api/v1/utils/health-check/")

        try:
            response = await self.client.get(f"{self.base_url}/api/v1/utils/health-check/")
            print(f"   📊 响应状态码: {response.status_code}")
            print(f"   📄 响应内容: {response.text[:200]}")

            if response.status_code == 200:
                print("   ✅ Backend API 健康检查通过")
                return True
            else:
                print(f"   ❌ Backend API 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ Backend API 连接失败: {e}")
            return False

    async def test_user_auth(self) -> bool:
        """测试用户认证"""
        print("🔐 测试用户认证...")

        # 测试登录（使用默认超级用户）
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }

        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/login/access-token",
                data=login_data
            )

            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                print("   ✅ 用户认证成功")
                return True
            else:
                print(f"   ❌ 用户认证失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"   ❌ 用户认证异常: {e}")
            return False

    async def test_document_upload(self) -> bool:
        """测试文档创建"""
        print("📄 测试文档创建...")

        if not self.access_token:
            print("   ❌ 需要先进行用户认证")
            return False

        # 创建测试文档数据（JSON格式，不是文件上传）
        content = """人工智能（AI）是计算机科学的一个重要分支。

## 发展阶段
1. 符号主义阶段（1950s-1980s）
2. 连接主义阶段（1980s-2000s）
3. 深度学习阶段（2000s-至今）

## 关键技术
- 机器学习
- 深度学习
- 自然语言处理
- 计算机视觉"""

        document_data = {
            "title": "人工智能发展史",
            "content": content,
            "description": "AI发展历程概述",
            "file_type": "text/markdown",
            "size": len(content.encode('utf-8'))
        }

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/documents/",
                json=document_data,
                headers=headers
            )

            if response.status_code in [200, 201]:
                data = response.json()
                self.test_document_id = data.get("id")
                print(f"   ✅ 文档创建成功: {self.test_document_id}")
                return True
            else:
                print(f"   ❌ 文档创建失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"   ❌ 文档创建异常: {e}")
            return False

    async def run_end_to_end_tests(self) -> bool:
        """运行端到端测试"""
        print("🚀 开始端到端测试...")
        print("=" * 60)

        e2e_results = {}

        # 端到端测试流程
        e2e_results["backend_health"] = await self.test_backend_health()
        e2e_results["user_auth"] = await self.test_user_auth()
        e2e_results["document_create"] = await self.test_document_upload()

        # 统计结果
        passed = sum(1 for result in e2e_results.values() if result)
        total = len(e2e_results)

        print("\n" + "=" * 60)
        print(f"📊 端到端测试结果: {passed}/{total} 通过")

        # 显示详细结果
        print("\n📋 详细结果:")
        for test_name, result in e2e_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")

        await self.client.aclose()
        return passed == total

async def main():
    """主函数"""
    tester = SystemIntegrationTester()

    try:
        # 运行基础集成测试
        print("第一阶段：基础服务集成测试")
        basic_success = tester.run_integration_tests()

        print("\n" + "="*60)
        print("第二阶段：端到端功能测试")
        e2e_success = await tester.run_end_to_end_tests()

        overall_success = basic_success and e2e_success

        print("\n" + "="*60)
        if overall_success:
            print("🎉 所有测试通过！系统运行正常。")
        else:
            print("⚠️  部分测试失败，请检查相关服务。")

        return 0 if overall_success else 1
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
