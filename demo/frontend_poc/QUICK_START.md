# 🚀 Master-Know 前端POC 快速启动指南

## 📋 前置条件

确保您的开发环境满足以下要求：

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **后端服务**: Master-Know后端API正在运行
- **操作系统**: macOS, Linux, 或 Windows

## ⚡ 快速启动（5分钟）

### 1. 进入项目目录
```bash
cd demo/frontend_poc
```

### 2. 安装依赖
```bash
npm install
```

### 3. 验证后端连接
```bash
# 检查后端API是否可访问
curl http://api.localhost.tiangolo.com/api/v1/utils/health-check/
```

### 4. 启动开发服务器
```bash
# 方式1：使用npm脚本
npm run dev

# 方式2：使用Python启动脚本
python3 run_poc.py
```

### 5. 访问应用
打开浏览器访问：http://localhost:5173

## 🔧 故障排除

### 问题1：依赖安装失败
```bash
# 清理缓存重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### 问题2：TypeScript编译错误
```bash
# 跳过库检查进行编译
npx tsc --noEmit --skipLibCheck
```

### 问题3：后端连接失败
```bash
# 检查后端服务状态
docker compose ps

# 重启后端服务
docker compose restart backend
```

### 问题4：端口冲突
```bash
# 使用不同端口启动
npm run dev -- --port 5174
```

## 🧪 运行测试

### 基础测试
```bash
# 运行项目结构测试
python3 test_poc.py
```

### 集成测试
```bash
# 运行前后端集成测试
python3 integration_test.py
```

### 构建测试
```bash
# 测试生产构建
npm run build
```

## 📱 默认登录信息

使用以下凭据登录系统：

- **用户名**: <EMAIL>
- **密码**: changethis

## 🎯 核心功能验证

登录后，您可以测试以下功能：

### 1. 文档管理
- 创建新文档
- 查看文档列表
- 处理文档内容

### 2. 智能搜索
- 文本搜索
- 向量搜索
- 混合搜索

### 3. AI对话
- 创建对话会话
- 智能问答
- 上下文感知对话

### 4. 主题管理
- 创建知识主题
- 关联文档
- 主题统计

## 🔍 开发工具

### 浏览器开发工具
- **React DevTools**: 调试React组件
- **TanStack Query DevTools**: 查看API请求状态
- **Network Tab**: 监控API调用

### VS Code扩展推荐
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag"
  ]
}
```

## 📊 性能监控

### 开发环境监控
```bash
# 查看构建分析
npm run build -- --analyze

# 监控内存使用
npm run dev -- --profile
```

### 网络请求监控
在浏览器开发者工具中：
1. 打开Network标签
2. 筛选XHR/Fetch请求
3. 监控API响应时间

## 🛠️ 自定义配置

### 环境变量配置
编辑`.env`文件：
```env
# API配置
VITE_API_BASE_URL=http://your-api-server.com/api/v1
VITE_WS_BASE_URL=ws://your-api-server.com/api/v1

# 应用配置
VITE_APP_TITLE=Your App Name
VITE_DEV_MODE=true
```

### Vite配置自定义
编辑`vite.config.ts`：
```typescript
export default defineConfig({
  server: {
    port: 3000,
    host: '0.0.0.0',
    proxy: {
      '/api': 'http://localhost:8000'
    }
  }
});
```

## 📚 开发指南

### 添加新页面
1. 在`src/routes/`创建路由文件
2. 在`src/pages/`创建页面组件
3. 更新路由配置

### 添加新API
1. 在`src/api/`创建API模块
2. 在`src/types/`添加类型定义
3. 创建对应的Hook

### 添加新组件
1. 在`src/components/`创建组件
2. 使用Chakra UI组件库
3. 确保响应式设计

## 🚨 常见错误解决

### TypeScript错误
```bash
# 临时跳过类型检查
export TS_NODE_COMPILER_OPTIONS='{"skipLibCheck": true}'
npm run dev
```

### 依赖冲突
```bash
# 查看依赖树
npm ls

# 解决peer依赖警告
npm install --legacy-peer-deps
```

### 热重载失败
```bash
# 重启开发服务器
Ctrl+C
npm run dev
```

## 📞 获取帮助

### 文档资源
- [项目README](./README.md)
- [API文档](../../docs/API.md)
- [探索经验](./EXPLORATION_INSIGHTS.md)
- [代码陷阱](./CODE_PITFALLS.md)

### 调试信息收集
遇到问题时，请收集以下信息：
```bash
# 系统信息
node --version
npm --version

# 项目信息
npm list --depth=0

# 错误日志
npm run dev 2>&1 | tee debug.log
```

## 🎉 成功启动检查清单

- [ ] 依赖安装成功
- [ ] 开发服务器启动
- [ ] 浏览器可以访问应用
- [ ] 可以成功登录
- [ ] API请求正常响应
- [ ] 基本功能可用

完成以上检查后，您就可以开始基于此POC进行前端开发了！

---

**提示**: 如果遇到任何问题，请先查看[代码陷阱文档](./CODE_PITFALLS.md)中的常见问题解决方案。
