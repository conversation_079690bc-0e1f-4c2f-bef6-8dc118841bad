import React, { useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Heading,
  Text,
  Alert,
  AlertIcon,
  Card,
  CardBody,
  useToast,
} from '@chakra-ui/react';
import { useAuth } from '@/hooks/useAuth';

interface LoginFormProps {
  onSuccess?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onSuccess }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { login } = useAuth();
  const toast = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // 清除错误信息
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username || !formData.password) {
      setError('请填写用户名和密码');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const result = await login({
        username: formData.username,
        password: formData.password,
      });

      if (result.success) {
        toast({
          title: '登录成功',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        onSuccess?.();
      } else {
        setError(result.error || '登录失败');
      }
    } catch (error: any) {
      setError(error.message || '登录失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card maxW="md" mx="auto">
      <CardBody>
        <VStack spacing={6}>
          <Heading size="lg" textAlign="center">
            登录 Master-Know
          </Heading>
          
          <Text color="gray.600" textAlign="center">
            请输入您的账号信息
          </Text>

          {error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              {error}
            </Alert>
          )}

          <Box as="form" onSubmit={handleSubmit} w="100%">
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>用户名/邮箱</FormLabel>
                <Input
                  name="username"
                  type="email"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="请输入邮箱地址"
                  disabled={isSubmitting}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>密码</FormLabel>
                <Input
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="请输入密码"
                  disabled={isSubmitting}
                />
              </FormControl>

              <Button
                type="submit"
                colorScheme="blue"
                size="lg"
                w="100%"
                isLoading={isSubmitting}
                loadingText="登录中..."
              >
                登录
              </Button>
            </VStack>
          </Box>

          <Text fontSize="sm" color="gray.500" textAlign="center">
            默认管理员账号: <EMAIL> / changethis
          </Text>
        </VStack>
      </CardBody>
    </Card>
  );
};
