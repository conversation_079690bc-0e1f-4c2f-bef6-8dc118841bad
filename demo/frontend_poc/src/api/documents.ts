import { apiRequest } from './client';
import { 
  Document, 
  DocumentCreate, 
  DocumentUpdate, 
  DocumentChunk,
  PaginatedResponse 
} from '@/types/api';

export const documentsApi = {
  // 获取文档列表
  getDocuments: async (params?: {
    skip?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Document>> => {
    const queryParams = new URLSearchParams();
    if (params?.skip !== undefined) queryParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) queryParams.append('limit', params.limit.toString());
    
    const url = `/documents/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest.get(url);
  },

  // 获取单个文档
  getDocument: async (id: string): Promise<Document> => {
    return apiRequest.get(`/documents/${id}`);
  },

  // 创建文档
  createDocument: async (documentData: DocumentCreate): Promise<Document> => {
    return apiRequest.post('/documents/', documentData);
  },

  // 更新文档
  updateDocument: async (id: string, documentData: DocumentUpdate): Promise<Document> => {
    return apiRequest.put(`/documents/${id}`, documentData);
  },

  // 删除文档
  deleteDocument: async (id: string): Promise<{ message: string }> => {
    return apiRequest.delete(`/documents/${id}`);
  },

  // 获取文档块
  getDocumentChunks: async (id: string): Promise<PaginatedResponse<DocumentChunk>> => {
    return apiRequest.get(`/documents/${id}/chunks`);
  },

  // 处理文档
  processDocument: async (id: string): Promise<{ message: string }> => {
    return apiRequest.post(`/documents/${id}/process`);
  },

  // 重新处理文档
  reprocessDocument: async (id: string): Promise<{ message: string }> => {
    return apiRequest.post(`/documents/${id}/reprocess`);
  },

  // 获取文档统计
  getDocumentStats: async (id: string): Promise<{
    total_chunks: number;
    total_tokens: number;
    processing_status: string;
  }> => {
    return apiRequest.get(`/documents/${id}/stats`);
  },
};
