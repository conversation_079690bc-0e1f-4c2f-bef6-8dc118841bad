import { apiRequest } from './client';
import { 
  Conversation, 
  ConversationCreate, 
  ConversationMessage,
  ChatRequest,
  ChatResponse,
  PaginatedResponse 
} from '@/types/api';

export const conversationsApi = {
  // 获取对话列表
  getConversations: async (params?: {
    skip?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Conversation>> => {
    const queryParams = new URLSearchParams();
    if (params?.skip !== undefined) queryParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) queryParams.append('limit', params.limit.toString());
    
    const url = `/conversations/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest.get(url);
  },

  // 获取单个对话
  getConversation: async (id: string): Promise<Conversation> => {
    return apiRequest.get(`/conversations/${id}`);
  },

  // 创建对话
  createConversation: async (conversationData: ConversationCreate): Promise<Conversation> => {
    return apiRequest.post('/conversations/', conversationData);
  },

  // 获取对话消息
  getConversationMessages: async (
    conversationId: string,
    params?: {
      skip?: number;
      limit?: number;
    }
  ): Promise<PaginatedResponse<ConversationMessage>> => {
    const queryParams = new URLSearchParams();
    if (params?.skip !== undefined) queryParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) queryParams.append('limit', params.limit.toString());
    
    const url = `/conversations/${conversationId}/messages${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest.get(url);
  },

  // 智能聊天
  chat: async (chatData: ChatRequest): Promise<ChatResponse> => {
    return apiRequest.post('/conversations/chat', chatData);
  },

  // 获取WebSocket连接URL
  getWebSocketUrl: (conversationId: string): string => {
    const wsBaseUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://api.localhost.tiangolo.com/api/v1';
    return `${wsBaseUrl}/conversations/ws/${conversationId}`;
  },
};
