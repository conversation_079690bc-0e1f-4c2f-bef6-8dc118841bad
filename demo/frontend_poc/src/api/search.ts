import { apiRequest } from './client';
import { SearchRequest, SearchResponse } from '@/types/api';

export const searchApi = {
  // 搜索文档 (POST)
  searchDocuments: async (searchData: SearchRequest): Promise<SearchResponse> => {
    return apiRequest.post('/search/documents', searchData);
  },

  // 搜索文档 (GET) - 用于简单查询
  searchDocumentsGet: async (params: {
    q: string;
    search_type?: 'text' | 'vector' | 'hybrid';
    limit?: number;
    offset?: number;
  }): Promise<SearchResponse> => {
    const queryParams = new URLSearchParams();
    queryParams.append('q', params.q);
    if (params.search_type) queryParams.append('search_type', params.search_type);
    if (params.limit !== undefined) queryParams.append('limit', params.limit.toString());
    if (params.offset !== undefined) queryParams.append('offset', params.offset.toString());
    
    return apiRequest.get(`/search/documents?${queryParams.toString()}`);
  },

  // 语义搜索
  semanticSearch: async (searchData: {
    query: string;
    limit?: number;
    threshold?: number;
  }): Promise<{
    results: Array<{
      content: string;
      source: string;
      score: number;
      chunk_id: string;
    }>;
    total: number;
  }> => {
    return apiRequest.post('/search/semantic', searchData);
  },

  // 初始化搜索服务 (管理员功能)
  initializeSearch: async (): Promise<{ message: string; success: boolean }> => {
    return apiRequest.post('/search/initialize');
  },
};
