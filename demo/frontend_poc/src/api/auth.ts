import { apiRequest, formRequest } from './client';
import { LoginRequest, LoginResponse, User } from '@/types/api';

export const authApi = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    return formRequest.post('/login/access-token', credentials);
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    return apiRequest.get('/users/me');
  },

  // 用户注册
  register: async (userData: {
    email: string;
    password: string;
    full_name?: string;
  }): Promise<User> => {
    return apiRequest.post('/users/signup', userData);
  },

  // 更新用户信息
  updateProfile: async (userData: {
    full_name?: string;
    email?: string;
  }): Promise<User> => {
    return apiRequest.patch('/users/me', userData);
  },

  // 修改密码
  changePassword: async (passwordData: {
    current_password: string;
    new_password: string;
  }): Promise<{ message: string }> => {
    return apiRequest.patch('/users/me/password', passwordData);
  },

  // 登出（清除本地token）
  logout: () => {
    localStorage.removeItem('master_know_access_token');
    localStorage.removeItem('master_know_user_info');
  },
};
