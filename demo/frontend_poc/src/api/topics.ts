import { apiRequest } from './client';
import { Topic, TopicCreate, TopicUpdate, PaginatedResponse } from '@/types/api';

export const topicsApi = {
  // 获取主题列表
  getTopics: async (params?: {
    skip?: number;
    limit?: number;
  }): Promise<PaginatedResponse<Topic>> => {
    const queryParams = new URLSearchParams();
    if (params?.skip !== undefined) queryParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) queryParams.append('limit', params.limit.toString());
    
    const url = `/topics/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return apiRequest.get(url);
  },

  // 获取单个主题
  getTopic: async (id: string): Promise<Topic> => {
    return apiRequest.get(`/topics/${id}`);
  },

  // 创建主题
  createTopic: async (topicData: TopicCreate): Promise<Topic> => {
    return apiRequest.post('/topics/', topicData);
  },

  // 更新主题
  updateTopic: async (id: string, topicData: TopicUpdate): Promise<Topic> => {
    return apiRequest.put(`/topics/${id}`, topicData);
  },

  // 删除主题
  deleteTopic: async (id: string): Promise<{ message: string }> => {
    return apiRequest.delete(`/topics/${id}`);
  },

  // 获取主题统计
  getTopicStats: async (id: string): Promise<{
    topic_id: string;
    document_count: number;
    conversation_count: number;
    last_activity?: string;
  }> => {
    return apiRequest.get(`/topics/${id}/stats`);
  },

  // 关联文档到主题
  attachDocument: async (topicId: string, documentId: string): Promise<{ message: string }> => {
    return apiRequest.post(`/topics/${topicId}/documents/${documentId}`);
  },

  // 获取用户主题统计
  getUserStats: async (): Promise<{
    user_id: string;
    total_topics: number;
    active_topics: number;
    total_documents: number;
    total_conversations: number;
  }> => {
    return apiRequest.get('/topics/stats/user');
  },
};
