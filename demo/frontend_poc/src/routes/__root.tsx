import React from 'react';
import { createRootRoute, Outlet } from '@tanstack/react-router';
import { Box, Flex, Spinner, Center } from '@chakra-ui/react';

export const Route = createRootRoute({
  component: () => (
    <Box minH="100vh" bg="gray.50">
      <Outlet />
    </Box>
  ),
  pendingComponent: () => (
    <Center h="100vh">
      <Spinner size="xl" color="blue.500" />
    </Center>
  ),
});
