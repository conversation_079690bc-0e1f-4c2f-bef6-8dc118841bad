import { useState, useEffect, useCallback } from 'react';
import { authApi } from '@/api/auth';
import { User, LoginRequest } from '@/types/api';
import { STORAGE_KEYS } from '@/utils/constants';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // 检查本地存储的token和用户信息
  const checkAuthStatus = useCallback(async () => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);

      if (token && userInfo) {
        // 验证token是否有效
        const user = await authApi.getCurrentUser();
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
        // 更新本地存储的用户信息
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
      } else {
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      }
    } catch (error: any) {
      // Token无效，清除本地存储
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: error.message || '认证失败',
      });
    }
  }, []);

  // 登录
  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const loginResponse = await authApi.login(credentials);
      localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, loginResponse.access_token);
      
      const user = await authApi.getCurrentUser();
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(user));
      
      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
      
      return { success: true };
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || '登录失败',
      }));
      return { success: false, error: error.message || '登录失败' };
    }
  }, []);

  // 注册
  const register = useCallback(async (userData: {
    email: string;
    password: string;
    full_name?: string;
  }) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const user = await authApi.register(userData);
      
      // 注册成功后自动登录
      const loginResult = await login({
        username: userData.email,
        password: userData.password,
      });
      
      return loginResult;
    } catch (error: any) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || '注册失败',
      }));
      return { success: false, error: error.message || '注册失败' };
    }
  }, [login]);

  // 登出
  const logout = useCallback(() => {
    authApi.logout();
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
  }, []);

  // 更新用户信息
  const updateProfile = useCallback(async (userData: {
    full_name?: string;
    email?: string;
  }) => {
    try {
      const updatedUser = await authApi.updateProfile(userData);
      setAuthState(prev => ({
        ...prev,
        user: updatedUser,
      }));
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(updatedUser));
      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message || '更新失败' };
    }
  }, []);

  // 修改密码
  const changePassword = useCallback(async (passwordData: {
    current_password: string;
    new_password: string;
  }) => {
    try {
      await authApi.changePassword(passwordData);
      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message || '密码修改失败' };
    }
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return {
    ...authState,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    checkAuthStatus,
  };
};
