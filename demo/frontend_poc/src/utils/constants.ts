// 应用常量配置

export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://api.localhost.tiangolo.com/api/v1',
  WS_BASE_URL: import.meta.env.VITE_WS_BASE_URL || 'ws://api.localhost.tiangolo.com/api/v1',
  TIMEOUT: 30000,
} as const;

export const APP_CONFIG = {
  TITLE: import.meta.env.VITE_APP_TITLE || 'Master-Know',
  VERSION: import.meta.env.VITE_APP_VERSION || '0.1.0',
  DEV_MODE: import.meta.env.VITE_DEV_MODE === 'true',
} as const;

export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'master_know_access_token',
  USER_INFO: 'master_know_user_info',
  THEME: 'master_know_theme',
} as const;

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  DOCUMENTS: '/documents',
  SEARCH: '/search',
  CHAT: '/chat',
  TOPICS: '/topics',
  PROFILE: '/profile',
} as const;

export const SEARCH_TYPES = {
  TEXT: 'text',
  VECTOR: 'vector',
  HYBRID: 'hybrid',
} as const;

export const MESSAGE_ROLES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
} as const;

export const WEBSOCKET_EVENTS = {
  CONNECTION_ESTABLISHED: 'connection_established',
  PROCESSING: 'processing',
  AI_RESPONSE: 'ai_response',
  ERROR: 'error',
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;
