// API响应类型定义

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  detail?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  skip?: number;
  limit?: number;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface User {
  id: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
}

// 文档相关类型
export interface Document {
  id: string;
  title: string;
  content: string;
  description?: string;
  file_type: string;
  size: number;
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentCreate {
  title: string;
  content: string;
  description?: string;
  file_type: string;
  size?: number;
}

export interface DocumentUpdate {
  title?: string;
  content?: string;
  description?: string;
}

export interface DocumentChunk {
  id: string;
  document_id: string;
  content: string;
  chunk_index: number;
  start_char: number;
  end_char: number;
  token_count?: number;
  created_at: string;
}

// 搜索相关类型
export interface SearchRequest {
  query: string;
  search_type?: 'text' | 'vector' | 'hybrid';
  limit?: number;
  offset?: number;
}

export interface SearchResponse {
  hits: SearchHit[];
  total: number;
  took: number;
  timed_out: boolean;
  error?: string;
}

export interface SearchHit {
  document_id: string;
  chunk_id: string;
  content: string;
  score: number;
  highlights?: string[];
}

// 主题相关类型
export interface Topic {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface TopicCreate {
  name: string;
  description?: string;
  tags?: string[];
}

export interface TopicUpdate {
  name?: string;
  description?: string;
  tags?: string[];
}

// 对话相关类型
export interface Conversation {
  id: string;
  title: string;
  description?: string;
  topic_id?: string;
  owner_id: string;
  status: 'active' | 'archived' | 'deleted';
  created_at: string;
  updated_at: string;
}

export interface ConversationCreate {
  title: string;
  description?: string;
  topic_id?: string;
}

export interface ConversationMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  created_at: string;
}

export interface ChatRequest {
  message: string;
  conversation_id?: string;
  topic_id?: string;
  max_tokens?: number;
  temperature?: number;
  use_context?: boolean;
}

export interface ChatResponse {
  message: string;
  conversation_id: string;
  message_id: string;
  context_used?: any[];
  summary_generated?: boolean;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'connection_established' | 'processing' | 'ai_response' | 'error';
  conversation_id?: string;
  message?: string;
  timestamp?: string;
  data?: any;
}
