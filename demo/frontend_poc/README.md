# Frontend POC - Master-Know 前端概念验证

## 概述

这是 Master-Know 系统的前端概念验证（POC），展示如何与后端API进行集成，实现核心功能的前端界面。

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **TanStack Router** - 路由管理
- **Chakra UI** - UI组件库
- **TanStack Query** - 数据获取和状态管理
- **Axios** - HTTP客户端

## 核心功能

### 1. 用户认证
- 登录/注册
- JWT Token 管理
- 受保护的路由

### 2. 文档管理
- 文档上传和创建
- 文档列表展示
- 文档详情查看
- 文档处理状态

### 3. 智能搜索
- 文档搜索界面
- 搜索结果展示
- 搜索类型选择（文本/向量/混合）

### 4. 对话系统
- 智能聊天界面
- 实时WebSocket通信
- 对话历史管理
- 上下文感知对话

### 5. 主题管理
- 主题创建和管理
- 主题关联文档
- 主题统计信息

## 项目结构

```
demo/frontend_poc/
├── README.md                 # 项目说明
├── package.json             # 依赖配置
├── vite.config.ts           # Vite配置
├── tsconfig.json            # TypeScript配置
├── index.html               # HTML模板
├── src/
│   ├── main.tsx             # 应用入口
│   ├── App.tsx              # 主应用组件
│   ├── api/                 # API客户端
│   │   ├── client.ts        # HTTP客户端配置
│   │   ├── auth.ts          # 认证API
│   │   ├── documents.ts     # 文档API
│   │   ├── search.ts        # 搜索API
│   │   ├── conversations.ts # 对话API
│   │   └── topics.ts        # 主题API
│   ├── components/          # 可复用组件
│   │   ├── Layout/          # 布局组件
│   │   ├── Auth/            # 认证组件
│   │   ├── Documents/       # 文档组件
│   │   ├── Search/          # 搜索组件
│   │   ├── Chat/            # 聊天组件
│   │   └── Topics/          # 主题组件
│   ├── pages/               # 页面组件
│   │   ├── Login.tsx        # 登录页
│   │   ├── Dashboard.tsx    # 仪表板
│   │   ├── Documents.tsx    # 文档管理
│   │   ├── Search.tsx       # 搜索页面
│   │   ├── Chat.tsx         # 聊天页面
│   │   └── Topics.tsx       # 主题管理
│   ├── hooks/               # 自定义Hooks
│   │   ├── useAuth.ts       # 认证Hook
│   │   ├── useWebSocket.ts  # WebSocket Hook
│   │   └── useApi.ts        # API Hook
│   ├── types/               # TypeScript类型定义
│   │   ├── api.ts           # API类型
│   │   ├── auth.ts          # 认证类型
│   │   └── common.ts        # 通用类型
│   └── utils/               # 工具函数
│       ├── constants.ts     # 常量定义
│       ├── storage.ts       # 本地存储
│       └── websocket.ts     # WebSocket工具
└── public/                  # 静态资源
    └── favicon.ico
```

## 快速开始

### 1. 安装依赖

```bash
cd demo/frontend_poc
npm install
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
VITE_API_BASE_URL=http://api.localhost.tiangolo.com/api/v1
VITE_WS_BASE_URL=ws://api.localhost.tiangolo.com/api/v1
```

### 3. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:5173

### 4. 构建生产版本

```bash
npm run build
```

## API集成示例

### 认证流程

```typescript
// 登录
const loginResponse = await authApi.login({
  username: '<EMAIL>',
  password: 'changethis'
});

// 存储Token
localStorage.setItem('token', loginResponse.access_token);

// 使用Token访问受保护的API
const documents = await documentsApi.getDocuments();
```

### 文档管理

```typescript
// 创建文档
const document = await documentsApi.createDocument({
  title: '测试文档',
  content: '文档内容...',
  file_type: 'text/plain'
});

// 处理文档
await documentsApi.processDocument(document.id);

// 获取文档块
const chunks = await documentsApi.getDocumentChunks(document.id);
```

### 智能搜索

```typescript
// 搜索文档
const searchResults = await searchApi.searchDocuments({
  query: '深度学习',
  search_type: 'hybrid',
  limit: 10
});
```

### 实时聊天

```typescript
// 建立WebSocket连接
const ws = new WebSocket(`${WS_BASE_URL}/conversations/ws/${conversationId}`);

// 发送消息
ws.send(JSON.stringify({
  message: '什么是深度学习？',
  timestamp: new Date().toISOString()
}));

// 接收消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('收到AI回复:', data);
};
```

## 开发指南

### 1. 添加新的API端点

1. 在 `src/api/` 目录下创建或更新API模块
2. 定义TypeScript类型
3. 实现API调用函数
4. 在组件中使用TanStack Query进行数据获取

### 2. 创建新页面

1. 在 `src/pages/` 目录下创建页面组件
2. 配置路由
3. 实现页面逻辑和UI

### 3. 添加新组件

1. 在 `src/components/` 目录下创建组件
2. 使用Chakra UI组件库
3. 确保组件的可复用性

## 部署说明

### 开发环境
- 前端: http://localhost:5173
- 后端API: http://api.localhost.tiangolo.com

### 生产环境
- 需要配置正确的API地址
- 设置HTTPS和WSS
- 配置反向代理

## 相关文档

- [API文档](../../docs/API.md)
- [前端集成指南](../../docs/FRONTEND_INTEGRATION.md)
- [部署指南](../../docs/DEPLOYMENT.md)
