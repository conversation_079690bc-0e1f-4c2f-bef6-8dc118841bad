#!/usr/bin/env python3
"""
Frontend POC 启动脚本

这个脚本用于启动前端POC开发服务器，并进行基本的环境检查。
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 Master-Know Frontend POC 启动脚本")
    print("=" * 60)
    print()

def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js 版本: {version}")
            return True
        else:
            print("❌ Node.js 未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js 未安装")
        return False

def check_npm_version():
    """检查npm版本"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ npm 版本: {version}")
            return True
        else:
            print("❌ npm 未安装")
            return False
    except FileNotFoundError:
        print("❌ npm 未安装")
        return False

def check_package_json():
    """检查package.json文件"""
    package_json_path = Path("package.json")
    if package_json_path.exists():
        print("✅ package.json 文件存在")
        try:
            with open(package_json_path, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                print(f"   项目名称: {package_data.get('name', 'Unknown')}")
                print(f"   项目版本: {package_data.get('version', 'Unknown')}")
            return True
        except json.JSONDecodeError:
            print("❌ package.json 格式错误")
            return False
    else:
        print("❌ package.json 文件不存在")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_path = Path(".env")
    if env_path.exists():
        print("✅ .env 文件存在")
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        key = line.split('=')[0]
                        print(f"   {key}")
            return True
        except Exception as e:
            print(f"❌ 读取 .env 文件失败: {e}")
            return False
    else:
        print("⚠️  .env 文件不存在，将使用默认配置")
        return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖包...")
    try:
        result = subprocess.run(['npm', 'install'], check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def check_backend_connection():
    """检查后端连接"""
    print("\n🔗 检查后端API连接...")
    try:
        import requests
        api_url = os.getenv('VITE_API_BASE_URL', 'http://api.localhost.tiangolo.com/api/v1')
        health_url = f"{api_url}/utils/health-check/"
        
        response = requests.get(health_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ 后端API连接正常: {api_url}")
            return True
        else:
            print(f"⚠️  后端API响应异常: {response.status_code}")
            return False
    except ImportError:
        print("⚠️  requests库未安装，跳过后端连接检查")
        return True
    except Exception as e:
        print(f"⚠️  后端API连接失败: {e}")
        print("   请确保后端服务正在运行")
        return False

def start_dev_server():
    """启动开发服务器"""
    print("\n🚀 启动开发服务器...")
    print("   前端地址: http://localhost:5173")
    print("   按 Ctrl+C 停止服务器")
    print()
    
    try:
        subprocess.run(['npm', 'run', 'dev'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 开发服务器启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 开发服务器已停止")
        return True

def main():
    """主函数"""
    print_banner()
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🔍 环境检查...")
    
    # 检查必要的工具
    checks = [
        check_node_version(),
        check_npm_version(),
        check_package_json(),
        check_env_file(),
    ]
    
    if not all(checks):
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n✅ 环境检查通过")
    
    # 检查node_modules是否存在
    if not Path("node_modules").exists():
        if not install_dependencies():
            sys.exit(1)
    else:
        print("\n📦 依赖已安装")
    
    # 检查后端连接
    check_backend_connection()
    
    # 启动开发服务器
    start_dev_server()

if __name__ == "__main__":
    main()
