#!/usr/bin/env python3
"""
Frontend POC 测试脚本

测试前端POC的基本功能和API集成
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

def print_banner():
    """打印测试横幅"""
    print("=" * 60)
    print("🧪 Master-Know Frontend POC 测试脚本")
    print("=" * 60)
    print()

def test_project_structure():
    """测试项目结构"""
    print("📁 测试项目结构...")
    
    required_files = [
        "package.json",
        "vite.config.ts",
        "tsconfig.json",
        "index.html",
        "src/main.tsx",
        "src/App.tsx",
        "src/types/api.ts",
        "src/api/client.ts",
        "src/api/auth.ts",
        "src/hooks/useAuth.ts",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺失文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 项目结构完整")
    return True

def test_package_json():
    """测试package.json配置"""
    print("\n📦 测试package.json配置...")
    
    try:
        with open("package.json", 'r', encoding='utf-8') as f:
            package_data = json.load(f)
        
        # 检查必要的依赖
        required_deps = [
            "@chakra-ui/react",
            "@tanstack/react-query",
            "@tanstack/react-router",
            "axios",
            "react",
            "react-dom",
        ]
        
        dependencies = package_data.get("dependencies", {})
        missing_deps = []
        
        for dep in required_deps:
            if dep in dependencies:
                print(f"   ✅ {dep}: {dependencies[dep]}")
            else:
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"\n❌ 缺失依赖:")
            for dep in missing_deps:
                print(f"   - {dep}")
            return False
        
        # 检查脚本
        scripts = package_data.get("scripts", {})
        required_scripts = ["dev", "build"]
        
        for script in required_scripts:
            if script in scripts:
                print(f"   ✅ 脚本 {script}: {scripts[script]}")
            else:
                print(f"   ❌ 缺失脚本: {script}")
        
        print("✅ package.json 配置正确")
        return True
        
    except Exception as e:
        print(f"❌ package.json 测试失败: {e}")
        return False

def test_typescript_config():
    """测试TypeScript配置"""
    print("\n🔧 测试TypeScript配置...")
    
    try:
        with open("tsconfig.json", 'r', encoding='utf-8') as f:
            ts_config = json.load(f)
        
        compiler_options = ts_config.get("compilerOptions", {})
        
        # 检查关键配置
        key_options = {
            "target": "ES2020",
            "module": "ESNext",
            "jsx": "react-jsx",
            "strict": True,
        }
        
        for option, expected in key_options.items():
            actual = compiler_options.get(option)
            if actual == expected:
                print(f"   ✅ {option}: {actual}")
            else:
                print(f"   ⚠️  {option}: {actual} (期望: {expected})")
        
        print("✅ TypeScript 配置检查完成")
        return True
        
    except Exception as e:
        print(f"❌ TypeScript 配置测试失败: {e}")
        return False

def test_api_types():
    """测试API类型定义"""
    print("\n🔍 测试API类型定义...")
    
    try:
        # 读取API类型文件
        with open("src/types/api.ts", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类型定义
        required_types = [
            "LoginRequest",
            "LoginResponse",
            "User",
            "Document",
            "SearchRequest",
            "SearchResponse",
            "Conversation",
            "Topic",
        ]
        
        missing_types = []
        for type_name in required_types:
            if f"interface {type_name}" in content or f"type {type_name}" in content:
                print(f"   ✅ {type_name}")
            else:
                missing_types.append(type_name)
        
        if missing_types:
            print(f"\n❌ 缺失类型定义:")
            for type_name in missing_types:
                print(f"   - {type_name}")
            return False
        
        print("✅ API类型定义完整")
        return True
        
    except Exception as e:
        print(f"❌ API类型测试失败: {e}")
        return False

def test_backend_api():
    """测试后端API连接"""
    print("\n🌐 测试后端API连接...")
    
    try:
        # 读取环境配置
        api_base_url = "http://api.localhost.tiangolo.com/api/v1"
        
        # 测试健康检查端点
        health_url = f"{api_base_url.replace('/api/v1', '')}/api/v1/utils/health-check/"
        
        print(f"   测试URL: {health_url}")
        
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ 健康检查通过: {response.status_code}")
        else:
            print(f"   ⚠️  健康检查响应: {response.status_code}")
        
        # 测试OpenAPI文档
        docs_url = f"{api_base_url.replace('/api/v1', '')}/docs"
        docs_response = requests.get(docs_url, timeout=10)
        
        if docs_response.status_code == 200:
            print(f"   ✅ API文档可访问: {docs_url}")
        else:
            print(f"   ⚠️  API文档不可访问: {docs_response.status_code}")
        
        print("✅ 后端API连接测试完成")
        return True
        
    except Exception as e:
        print(f"⚠️  后端API连接测试失败: {e}")
        print("   这可能是因为后端服务未启动")
        return True  # 不阻止其他测试

def test_build_process():
    """测试构建过程"""
    print("\n🔨 测试构建过程...")
    
    try:
        import subprocess
        
        # 检查TypeScript编译
        print("   检查TypeScript编译...")
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit'], 
            capture_output=True, 
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("   ✅ TypeScript编译通过")
        else:
            print(f"   ❌ TypeScript编译失败:")
            print(f"   {result.stderr}")
            return False
        
        print("✅ 构建过程测试完成")
        return True
        
    except subprocess.TimeoutExpired:
        print("   ⚠️  TypeScript编译超时")
        return True
    except Exception as e:
        print(f"❌ 构建过程测试失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 运行所有测试
    tests = [
        test_project_structure,
        test_package_json,
        test_typescript_config,
        test_api_types,
        test_backend_api,
        test_build_process,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        print()
    
    # 输出测试结果
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！前端POC准备就绪")
        print("\n🚀 启动命令:")
        print("   python3 run_poc.py")
        print("   或者: npm run dev")
    else:
        print("⚠️  部分测试失败，请检查上述问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
