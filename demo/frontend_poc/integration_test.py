#!/usr/bin/env python3
"""
前后端集成测试脚本

测试前端POC与后端API的完整集成流程
"""

import os
import sys
import json
import time
import requests
import asyncio
import subprocess
from pathlib import Path

def print_banner():
    """打印测试横幅"""
    print("=" * 70)
    print("🔗 Master-Know 前后端集成测试")
    print("=" * 70)
    print()

def test_backend_health():
    """测试后端健康状态"""
    print("🏥 测试后端健康状态...")
    
    try:
        api_base = "http://api.localhost.tiangolo.com"
        
        # 测试健康检查
        health_response = requests.get(f"{api_base}/api/v1/utils/health-check/", timeout=10)
        if health_response.status_code == 200:
            print("   ✅ 后端健康检查通过")
        else:
            print(f"   ❌ 后端健康检查失败: {health_response.status_code}")
            return False
        
        # 测试OpenAPI文档
        docs_response = requests.get(f"{api_base}/docs", timeout=10)
        if docs_response.status_code == 200:
            print("   ✅ API文档可访问")
        else:
            print(f"   ⚠️  API文档访问异常: {docs_response.status_code}")
        
        # 测试OpenAPI JSON
        openapi_response = requests.get(f"{api_base}/openapi.json", timeout=10)
        if openapi_response.status_code == 200:
            openapi_data = openapi_response.json()
            paths_count = len(openapi_data.get("paths", {}))
            print(f"   ✅ OpenAPI规范可访问 ({paths_count} 个端点)")
        else:
            print(f"   ⚠️  OpenAPI规范访问异常: {openapi_response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 后端连接失败: {e}")
        return False

def test_authentication_flow():
    """测试认证流程"""
    print("\n🔐 测试认证流程...")
    
    try:
        api_base = "http://api.localhost.tiangolo.com/api/v1"
        
        # 测试登录
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        login_response = requests.post(
            f"{api_base}/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            access_token = token_data.get("access_token")
            print("   ✅ 登录成功，获取到访问令牌")
            
            # 测试获取用户信息
            headers = {"Authorization": f"Bearer {access_token}"}
            user_response = requests.get(f"{api_base}/users/me", headers=headers, timeout=10)
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                print(f"   ✅ 获取用户信息成功: {user_data.get('email')}")
                return access_token
            else:
                print(f"   ❌ 获取用户信息失败: {user_response.status_code}")
                return None
        else:
            print(f"   ❌ 登录失败: {login_response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 认证流程测试失败: {e}")
        return None

def test_document_api(token):
    """测试文档API"""
    print("\n📄 测试文档API...")
    
    try:
        api_base = "http://api.localhost.tiangolo.com/api/v1"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        
        # 创建测试文档
        doc_data = {
            "title": "前端POC测试文档",
            "content": "这是一个用于测试前后端集成的文档。包含一些测试内容，用于验证文档处理功能。",
            "description": "集成测试文档",
            "file_type": "text/plain",
            "size": 100
        }
        
        create_response = requests.post(
            f"{api_base}/documents/",
            json=doc_data,
            headers=headers,
            timeout=10
        )
        
        if create_response.status_code == 200:
            document = create_response.json()
            doc_id = document.get("id")
            print(f"   ✅ 创建文档成功: {doc_id}")
            
            # 获取文档列表
            list_response = requests.get(f"{api_base}/documents/", headers=headers, timeout=10)
            if list_response.status_code == 200:
                docs_data = list_response.json()
                print(f"   ✅ 获取文档列表成功: {docs_data.get('count', 0)} 个文档")
            else:
                print(f"   ⚠️  获取文档列表失败: {list_response.status_code}")
            
            # 处理文档
            process_response = requests.post(
                f"{api_base}/documents/{doc_id}/process",
                headers=headers,
                timeout=10
            )
            if process_response.status_code == 200:
                print("   ✅ 文档处理任务已启动")
            else:
                print(f"   ⚠️  文档处理启动失败: {process_response.status_code}")
            
            return doc_id
        else:
            print(f"   ❌ 创建文档失败: {create_response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 文档API测试失败: {e}")
        return None

def test_search_api(token):
    """测试搜索API"""
    print("\n🔍 测试搜索API...")
    
    try:
        api_base = "http://api.localhost.tiangolo.com/api/v1"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        
        # 测试文档搜索
        search_data = {
            "query": "测试",
            "search_type": "hybrid",
            "limit": 5,
            "offset": 0
        }
        
        search_response = requests.post(
            f"{api_base}/search/documents",
            json=search_data,
            headers=headers,
            timeout=10
        )
        
        if search_response.status_code == 200:
            search_results = search_response.json()
            hits_count = len(search_results.get("hits", []))
            print(f"   ✅ 搜索成功: 找到 {hits_count} 个结果")
            
            # 测试GET方式搜索
            get_search_response = requests.get(
                f"{api_base}/search/documents?q=测试&search_type=text&limit=3",
                headers=headers,
                timeout=10
            )
            
            if get_search_response.status_code == 200:
                print("   ✅ GET方式搜索成功")
            else:
                print(f"   ⚠️  GET方式搜索失败: {get_search_response.status_code}")
            
            return True
        else:
            print(f"   ❌ 搜索失败: {search_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 搜索API测试失败: {e}")
        return False

def test_topics_api(token):
    """测试主题API"""
    print("\n🏷️  测试主题API...")
    
    try:
        api_base = "http://api.localhost.tiangolo.com/api/v1"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        
        # 创建测试主题
        topic_data = {
            "name": "前端POC测试主题",
            "description": "用于测试前后端集成的主题",
            "tags": ["测试", "POC", "集成"]
        }
        
        create_response = requests.post(
            f"{api_base}/topics/",
            json=topic_data,
            headers=headers,
            timeout=10
        )
        
        if create_response.status_code == 200:
            topic = create_response.json()
            topic_id = topic.get("id")
            print(f"   ✅ 创建主题成功: {topic_id}")
            
            # 获取主题列表
            list_response = requests.get(f"{api_base}/topics/", headers=headers, timeout=10)
            if list_response.status_code == 200:
                topics_data = list_response.json()
                print(f"   ✅ 获取主题列表成功: {topics_data.get('count', 0)} 个主题")
            else:
                print(f"   ⚠️  获取主题列表失败: {list_response.status_code}")
            
            return topic_id
        else:
            print(f"   ❌ 创建主题失败: {create_response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 主题API测试失败: {e}")
        return None

def test_conversations_api(token):
    """测试对话API"""
    print("\n💬 测试对话API...")
    
    try:
        api_base = "http://api.localhost.tiangolo.com/api/v1"
        headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
        
        # 创建测试对话
        conversation_data = {
            "title": "前端POC测试对话",
            "description": "用于测试前后端集成的对话"
        }
        
        create_response = requests.post(
            f"{api_base}/conversations/",
            json=conversation_data,
            headers=headers,
            timeout=10
        )
        
        if create_response.status_code == 200:
            conversation = create_response.json()
            conv_id = conversation.get("id")
            print(f"   ✅ 创建对话成功: {conv_id}")
            
            # 测试智能聊天
            chat_data = {
                "message": "你好，这是一个测试消息",
                "conversation_id": conv_id,
                "use_context": True,
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            chat_response = requests.post(
                f"{api_base}/conversations/chat",
                json=chat_data,
                headers=headers,
                timeout=30
            )
            
            if chat_response.status_code == 200:
                chat_result = chat_response.json()
                print("   ✅ 智能聊天成功")
                print(f"   💭 AI回复: {chat_result.get('message', '')[:50]}...")
            else:
                print(f"   ⚠️  智能聊天失败: {chat_response.status_code}")
            
            return conv_id
        else:
            print(f"   ❌ 创建对话失败: {create_response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 对话API测试失败: {e}")
        return None

def test_frontend_build():
    """测试前端构建"""
    print("\n🔨 测试前端构建...")
    
    try:
        # 检查TypeScript编译
        print("   检查TypeScript编译...")
        result = subprocess.run(
            ['npx', 'tsc', '--noEmit'], 
            capture_output=True, 
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("   ✅ TypeScript编译通过")
        else:
            print(f"   ❌ TypeScript编译失败:")
            print(f"   {result.stderr}")
            return False
        
        # 测试Vite构建
        print("   测试Vite构建...")
        build_result = subprocess.run(
            ['npm', 'run', 'build'], 
            capture_output=True, 
            text=True,
            timeout=120
        )
        
        if build_result.returncode == 0:
            print("   ✅ Vite构建成功")
            
            # 检查构建产物
            dist_path = Path("dist")
            if dist_path.exists():
                files = list(dist_path.rglob("*"))
                print(f"   ✅ 构建产物生成: {len(files)} 个文件")
            else:
                print("   ⚠️  构建产物目录不存在")
            
            return True
        else:
            print(f"   ❌ Vite构建失败:")
            print(f"   {build_result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⚠️  构建超时")
        return False
    except Exception as e:
        print(f"   ❌ 前端构建测试失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 运行集成测试
    tests = [
        ("后端健康检查", test_backend_health),
        ("认证流程", test_authentication_flow),
        ("前端构建", test_frontend_build),
    ]
    
    passed = 0
    total = len(tests)
    token = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "认证流程":
                result = test_func()
                if result:
                    token = result
                    passed += 1
            elif test_name in ["文档API", "搜索API", "主题API", "对话API"] and token:
                if test_func(token):
                    passed += 1
            else:
                if test_func():
                    passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
        print()
    
    # 如果有token，继续测试API
    if token:
        api_tests = [
            ("文档API", test_document_api),
            ("搜索API", test_search_api),
            ("主题API", test_topics_api),
            ("对话API", test_conversations_api),
        ]
        
        for test_name, test_func in api_tests:
            try:
                if test_func(token):
                    passed += 1
                total += 1
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                total += 1
            print()
    
    # 输出测试结果
    print("=" * 70)
    print(f"📊 集成测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有集成测试通过！前后端集成正常")
        print("\n✨ 系统已准备就绪，可以开始前端开发")
    else:
        print("⚠️  部分测试失败，请检查相关问题")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
