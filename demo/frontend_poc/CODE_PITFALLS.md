# 前端开发代码陷阱总结

## 🚨 关键陷阱清单

### 1. Chakra UI v3.x 重大变更

**陷阱描述**：Chakra UI从v2.x升级到v3.x后，API发生了重大变化，很多组件的导入方式和属性都不兼容。

**错误代码示例**：
```typescript
// ❌ 错误：v2.x风格的导入
import { 
  FormControl, 
  FormLabel, 
  AlertIcon, 
  useToast,
  StatNumber,
  useColorModeValue 
} from '@chakra-ui/react';

// ❌ 错误：v2.x风格的属性
<Button isLoading={true} leftIcon={<Icon />} />
<VStack spacing={4} />
<Alert status="error" />
```

**正确解决方案**：
```typescript
// ✅ 正确：查阅v3.x文档，使用正确的导入
import { 
  Field,
  Button,
  Alert,
  Stack 
} from '@chakra-ui/react';

// ✅ 正确：使用v3.x的属性名
<Button loading={true} startIcon={<Icon />} />
<Stack gap={4} />
<Alert.Root status="error" />
```

**预防措施**：
1. 始终查阅当前版本的官方文档
2. 使用TypeScript严格模式捕获类型错误
3. 考虑版本锁定策略

### 2. Vite环境变量类型定义缺失

**陷阱描述**：TypeScript无法识别Vite的`import.meta.env`类型，导致编译错误。

**错误代码示例**：
```typescript
// ❌ 错误：TypeScript报错 Property 'env' does not exist
const apiUrl = import.meta.env.VITE_API_BASE_URL;
```

**正确解决方案**：
```typescript
// ✅ 正确：创建 src/vite-env.d.ts
/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string;
  readonly VITE_WS_BASE_URL: string;
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_DEV_MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 然后可以安全使用
const apiUrl = import.meta.env.VITE_API_BASE_URL;
```

### 3. TanStack Router路由类型问题

**陷阱描述**：路由创建和类型定义不匹配，导致编译错误。

**错误代码示例**：
```typescript
// ❌ 错误：参数类型不匹配
export const Route = createFileRoute('/')({
  component: IndexPage,
});
```

**正确解决方案**：
```typescript
// ✅ 正确：确保路由配置正确
import { createFileRoute } from '@tanstack/react-router';

function IndexPage() {
  return <div>Home</div>;
}

export const Route = createFileRoute('/')({
  component: IndexPage,
});
```

### 4. Node.js类型定义缺失

**陷阱描述**：使用Node.js类型（如`NodeJS.Timeout`）时TypeScript报错。

**错误代码示例**：
```typescript
// ❌ 错误：Cannot find namespace 'NodeJS'
const timeoutRef = useRef<NodeJS.Timeout | null>(null);
```

**正确解决方案**：
```bash
# ✅ 正确：安装Node.js类型定义
npm install --save-dev @types/node
```

```typescript
// 然后可以正常使用
const timeoutRef = useRef<NodeJS.Timeout | null>(null);
```

### 5. 未使用的导入和变量

**陷阱描述**：TypeScript严格模式下，未使用的导入和变量会导致编译错误。

**错误代码示例**：
```typescript
// ❌ 错误：导入但未使用
import React from 'react';
import { Router, Navigate } from '@tanstack/react-router';

function Component() {
  const user = await api.register(data); // 未使用
  return <div>Hello</div>;
}
```

**正确解决方案**：
```typescript
// ✅ 正确：移除未使用的导入
import { createFileRoute } from '@tanstack/react-router';

function Component() {
  // 使用变量或添加下划线前缀
  const _user = await api.register(data);
  return <div>Hello</div>;
}
```

## 🛠️ 通用预防策略

### 1. 依赖管理策略

```json
// package.json - 锁定主要版本
{
  "dependencies": {
    "@chakra-ui/react": "~3.8.0",
    "react": "~18.2.0",
    "@tanstack/react-router": "~1.19.0"
  }
}
```

### 2. TypeScript配置优化

```json
// tsconfig.json - 严格但实用的配置
{
  "compilerOptions": {
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "skipLibCheck": true
  }
}
```

### 3. 开发工具配置

```json
// .eslintrc.js - 合理的ESLint规则
{
  "rules": {
    "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_" }],
    "react-hooks/exhaustive-deps": "warn"
  }
}
```

## 🔍 调试技巧

### 1. 快速定位类型错误

```bash
# 使用skipLibCheck快速编译
npx tsc --noEmit --skipLibCheck

# 逐步修复错误
npx tsc --noEmit --maxNodeModuleJsDepth 0
```

### 2. 组件库版本检查

```bash
# 检查已安装的版本
npm list @chakra-ui/react

# 查看可用版本
npm view @chakra-ui/react versions --json
```

### 3. 运行时调试

```typescript
// 在开发环境中添加调试信息
if (import.meta.env.DEV) {
  console.log('API Config:', API_CONFIG);
  console.log('Environment:', import.meta.env);
}
```

## 📚 最佳实践建议

### 1. 渐进式开发

1. **先建立基础架构**：确保TypeScript编译通过
2. **逐步添加功能**：一次只添加一个功能模块
3. **持续测试验证**：每个模块完成后立即测试

### 2. 版本管理策略

1. **锁定主要版本**：避免意外的破坏性更新
2. **定期更新依赖**：但要在专门的分支中进行
3. **文档化变更**：记录每次重大依赖更新的影响

### 3. 代码质量保证

1. **使用TypeScript严格模式**：尽早发现类型错误
2. **配置合理的ESLint规则**：平衡严格性和实用性
3. **编写单元测试**：确保核心功能的稳定性

## 🎯 快速修复检查清单

当遇到编译错误时，按以下顺序检查：

- [ ] 是否安装了所有必要的类型定义包（`@types/node`等）
- [ ] 是否创建了`vite-env.d.ts`文件
- [ ] 是否使用了正确版本的组件库API
- [ ] 是否有未使用的导入需要清理
- [ ] 是否需要更新TypeScript配置
- [ ] 是否需要降级某些依赖版本

## 💡 经验总结

1. **版本兼容性是最大的陷阱**：新版本的组件库往往有破坏性变更
2. **TypeScript配置需要平衡**：过于严格会影响开发效率
3. **环境变量类型定义很重要**：Vite项目必须正确配置
4. **渐进式开发是关键**：不要一次性引入太多复杂功能

通过遵循这些经验和最佳实践，可以显著减少前端开发中的常见陷阱，提高开发效率和代码质量。
