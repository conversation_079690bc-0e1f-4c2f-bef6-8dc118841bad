{"name": "master-know-frontend-poc", "private": true, "version": "0.1.0", "type": "module", "description": "Master-Know Frontend Proof of Concept", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@chakra-ui/react": "^3.8.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.28.14", "@tanstack/react-router": "^1.19.1", "axios": "^1.7.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-icons": "^5.4.0"}, "devDependencies": {"@tanstack/router-devtools": "^1.19.1", "@tanstack/router-vite-plugin": "^1.19.0", "@types/node": "^24.3.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.4.14"}}