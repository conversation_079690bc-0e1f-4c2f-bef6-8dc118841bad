#
# Manticore Search configuration file
#
# Simplified configuration for Master-Know system
#

searchd
{
    # Listen on all interfaces
    listen = 9306:mysql41
    listen = 9312
    listen = 9308:http

    # Data directory
    data_dir = /var/lib/manticore

    # Logging
    log = /var/log/manticore/searchd.log
    query_log = /var/log/manticore/query.log

    # Basic settings
    network_timeout = 5
    client_timeout = 300
    pid_file = /var/run/manticore/searchd.pid

    # Disable binlog for simplicity
    binlog_path =

    # Basic performance settings
    max_packet_size = 8M
    max_filters = 256
    max_filter_values = 4096

    # Thread settings
    thread_stack = 128K

    # Cache settings
    qcache_max_bytes = 16M
    qcache_ttl_sec = 60

    # Shutdown settings
    shutdown_timeout = 3
    watchdog = 1
}

common
{
    lemmatizer_base = /usr/share/manticore
    plugin_dir = /usr/lib/manticore
}

# Tables will be created dynamically via API
