# 知深学习导师 (Master-Know)

**一个个人化 AI 学习伴行系统，旨在通过可追溯、可复盘的引导式对话，帮助用户真正内化知识。**

[![Test](https://github.com/fastapi/full-stack-fastapi-template/workflows/Test/badge.svg)](https://github.com/fastapi/full-stack-fastapi-template/actions?query=workflow%3ATest)
[![Coverage](https://coverage-badge.samuelcolvin.workers.dev/fastapi/full-stack-fastapi-template.svg)](https://coverage-badge.samuelcolvin.workers.dev/redirect/fastapi/full-stack-fastapi-template)

## 核心理念

我们相信，真正的学习不是一次性的“问答”，而是一个持续的、可回溯的探索过程。本项目致力于解决 AI 对话“聊后即忘”的痛点，将碎片化的学习沉淀为结构化的个人知识库。

其核心是验证一个假设：**一个具备长期主题记忆、且能通过交互式摘要轻松回顾的 AI 对话体验，是否能显著提升用户的学习效率和深度。**

## 快速开始

本项目使用 Docker Compose 进行环境管理，让您能一键启动所有服务。

### 前置要求
- Docker Desktop (macOS/Windows) 或 Docker Engine (Linux)
- 至少 4GB 可用内存
- 端口 5173, 9306, 9308, 6379, 5432 未被占用

### 部署步骤

1.  **配置环境**:
    ```bash
    cp .env.example .env
    # 编辑 .env 文件，确保 DOMAIN=localhost.tiangolo.com
    ```

2.  **启动服务**:
    ```bash
    # 推荐使用 watch 模式，支持代码热重载
    docker compose watch

    # 或者使用标准模式
    docker compose up -d
    ```

3.  **验证部署**:
    ```bash
    # 运行系统集成测试
    python3 test_system_integration.py
    ```

4.  **访问应用**:
    - **前端**: `http://localhost:5173`
    - **后端 API**: `http://api.localhost.tiangolo.com`
    - **API 文档**: `http://api.localhost.tiangolo.com/docs`
    - **数据库管理**: `http://localhost:8080` (Adminer)

### 故障排除

如果遇到网络连接问题，请参考 [故障排除文档](./docs/TROUBLESHOOTING.md)。

## 文档中心

我们为您准备了详尽的文档，帮助您深入了解项目的方方面面。**所有文档的入口都在这里：**

**[👉 点击这里，进入项目文档中心](./docs/INDEX.md)**

在文档中心，您可以找到：
- **产品定义**: 项目的目标、用户故事和功能需求。
- **架构设计**: 系统的高层视图、数据模型和技术选型。
- **工程指南**: 详细的开发、部署和测试流程。

## 技术栈

- **后端**: FastAPI, SQLModel, PostgreSQL
- **前端**: React, TypeScript, Chakra UI
- **核心引擎**: Manticore Search (用于全文+向量混合搜索)
- **异步任务**: Dramatiq
- **部署**: Docker, Traefik

## 参与贡献

我们欢迎任何形式的贡献！请参考我们的工程指南开始。

## 许可证

本项目基于 MIT 许可证。
