# 前端集成指南

## 快速开始

### 1. 环境配置

```javascript
// config.js
export const API_CONFIG = {
  BASE_URL: 'http://api.localhost.tiangolo.com/api/v1',
  WS_URL: 'ws://api.localhost.tiangolo.com/api/v1',
  TIMEOUT: 30000
};
```

### 2. API客户端封装

```javascript
// api/client.js
class ApiClient {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  setToken(token) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { Authorization: `Bearer ${this.token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
  }

  // 认证相关
  async login(email, password) {
    const formData = new URLSearchParams();
    formData.append('username', email);
    formData.append('password', password);

    const response = await fetch(`${this.baseURL}/login/access-token`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: formData,
    });

    const data = await response.json();
    this.setToken(data.access_token);
    return data;
  }

  async register(userData) {
    return this.request('/users/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  // 主题相关
  async getTopics(skip = 0, limit = 100) {
    return this.request(`/topics/?skip=${skip}&limit=${limit}`);
  }

  async createTopic(topicData) {
    return this.request('/topics/', {
      method: 'POST',
      body: JSON.stringify(topicData),
    });
  }

  // 文档相关
  async uploadDocument(documentData) {
    return this.request('/documents/', {
      method: 'POST',
      body: JSON.stringify(documentData),
    });
  }

  async getDocuments(skip = 0, limit = 100) {
    return this.request(`/documents/?skip=${skip}&limit=${limit}`);
  }

  // 对话相关
  async createConversation(conversationData) {
    return this.request('/conversations/', {
      method: 'POST',
      body: JSON.stringify(conversationData),
    });
  }

  async sendMessage(messageData) {
    return this.request('/conversations/chat', {
      method: 'POST',
      body: JSON.stringify(messageData),
    });
  }

  async getConversationHistory(conversationId, skip = 0, limit = 50) {
    return this.request(`/conversations/${conversationId}/messages?skip=${skip}&limit=${limit}`);
  }
}

export const apiClient = new ApiClient(API_CONFIG.BASE_URL);
```

### 3. WebSocket管理器

```javascript
// websocket/manager.js
class WebSocketManager {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.connections = new Map();
  }

  connect(conversationId, callbacks = {}) {
    const url = `${this.baseURL}/conversations/ws/${conversationId}`;
    const ws = new WebSocket(url);

    ws.onopen = () => {
      console.log(`WebSocket connected for conversation ${conversationId}`);
      callbacks.onOpen?.(conversationId);
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      callbacks.onMessage?.(data);
    };

    ws.onclose = () => {
      console.log(`WebSocket closed for conversation ${conversationId}`);
      this.connections.delete(conversationId);
      callbacks.onClose?.(conversationId);
    };

    ws.onerror = (error) => {
      console.error(`WebSocket error for conversation ${conversationId}:`, error);
      callbacks.onError?.(error);
    };

    this.connections.set(conversationId, ws);
    return ws;
  }

  sendMessage(conversationId, message) {
    const ws = this.connections.get(conversationId);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        message,
        timestamp: new Date().toISOString(),
      }));
    }
  }

  disconnect(conversationId) {
    const ws = this.connections.get(conversationId);
    if (ws) {
      ws.close();
      this.connections.delete(conversationId);
    }
  }

  disconnectAll() {
    this.connections.forEach((ws, conversationId) => {
      this.disconnect(conversationId);
    });
  }
}

export const wsManager = new WebSocketManager(API_CONFIG.WS_URL);
```

### 4. React Hooks

```javascript
// hooks/useAuth.js
import { useState, useEffect } from 'react';
import { apiClient } from '../api/client';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      apiClient.setToken(token);
      fetchUser();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      const userData = await apiClient.request('/users/me');
      setUser(userData);
    } catch (error) {
      console.error('Failed to fetch user:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    const data = await apiClient.login(email, password);
    await fetchUser();
    return data;
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    apiClient.setToken(null);
    setUser(null);
  };

  return { user, login, logout, loading };
};

// hooks/useConversation.js
import { useState, useEffect } from 'react';
import { apiClient } from '../api/client';
import { wsManager } from '../websocket/manager';

export const useConversation = (conversationId) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    if (conversationId) {
      loadMessages();
      connectWebSocket();
    }

    return () => {
      wsManager.disconnect(conversationId);
    };
  }, [conversationId]);

  const loadMessages = async () => {
    setLoading(true);
    try {
      const response = await apiClient.getConversationHistory(conversationId);
      setMessages(response.data);
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const connectWebSocket = () => {
    wsManager.connect(conversationId, {
      onOpen: () => setConnected(true),
      onClose: () => setConnected(false),
      onMessage: (data) => {
        if (data.type === 'ai_response') {
          setMessages(prev => [...prev, {
            id: Date.now(),
            content: data.message,
            role: 'assistant',
            created_at: data.timestamp,
          }]);
        }
      },
      onError: (error) => {
        console.error('WebSocket error:', error);
        setConnected(false);
      },
    });
  };

  const sendMessage = async (content) => {
    // 添加用户消息到界面
    const userMessage = {
      id: Date.now(),
      content,
      role: 'user',
      created_at: new Date().toISOString(),
    };
    setMessages(prev => [...prev, userMessage]);

    // 通过WebSocket发送消息
    if (connected) {
      wsManager.sendMessage(conversationId, content);
    } else {
      // 如果WebSocket未连接，使用HTTP API
      try {
        const response = await apiClient.sendMessage({
          message: content,
          conversation_id: conversationId,
          use_context: true,
        });
        
        const aiMessage = {
          id: Date.now() + 1,
          content: response.message,
          role: 'assistant',
          created_at: response.timestamp,
        };
        setMessages(prev => [...prev, aiMessage]);
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    }
  };

  return { messages, sendMessage, loading, connected };
};

// hooks/useTopics.js
import { useState, useEffect } from 'react';
import { apiClient } from '../api/client';

export const useTopics = () => {
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(false);

  const loadTopics = async () => {
    setLoading(true);
    try {
      const response = await apiClient.getTopics();
      setTopics(response.data);
    } catch (error) {
      console.error('Failed to load topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const createTopic = async (topicData) => {
    const newTopic = await apiClient.createTopic(topicData);
    setTopics(prev => [...prev, newTopic]);
    return newTopic;
  };

  useEffect(() => {
    loadTopics();
  }, []);

  return { topics, createTopic, loadTopics, loading };
};
```

### 5. 组件示例

```javascript
// components/ChatInterface.jsx
import React, { useState } from 'react';
import { useConversation } from '../hooks/useConversation';

export const ChatInterface = ({ conversationId }) => {
  const { messages, sendMessage, loading, connected } = useConversation(conversationId);
  const [input, setInput] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim()) {
      sendMessage(input);
      setInput('');
    }
  };

  return (
    <div className="chat-interface">
      <div className="connection-status">
        {connected ? '🟢 实时连接' : '🔴 离线模式'}
      </div>
      
      <div className="messages">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.role}`}>
            <div className="content">{message.content}</div>
            <div className="timestamp">{message.created_at}</div>
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit} className="input-form">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="输入您的问题..."
          disabled={loading}
        />
        <button type="submit" disabled={loading || !input.trim()}>
          发送
        </button>
      </form>
    </div>
  );
};
```

### 6. 错误处理

```javascript
// utils/errorHandler.js
export const handleApiError = (error) => {
  if (error.response) {
    // API返回的错误
    const { status, data } = error.response;
    switch (status) {
      case 401:
        // 未认证，重定向到登录页
        window.location.href = '/login';
        break;
      case 403:
        // 权限不足
        alert('权限不足');
        break;
      case 422:
        // 验证错误
        alert(`验证错误: ${data.detail}`);
        break;
      default:
        alert(`请求失败: ${data.detail || '未知错误'}`);
    }
  } else {
    // 网络错误
    alert('网络连接失败，请检查网络设置');
  }
};
```

## 部署注意事项

### 开发环境
- API地址: `http://api.localhost.tiangolo.com`
- WebSocket地址: `ws://api.localhost.tiangolo.com`

### 生产环境
- 需要配置HTTPS和WSS
- 设置正确的CORS策略
- 配置反向代理

### 环境变量
```javascript
// .env
REACT_APP_API_URL=http://api.localhost.tiangolo.com/api/v1
REACT_APP_WS_URL=ws://api.localhost.tiangolo.com/api/v1
```
