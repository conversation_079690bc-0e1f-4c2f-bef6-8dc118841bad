# Master-Know API 参考文档

**基础URL**: `http://api.localhost.tiangolo.com/api/v1`  
**WebSocket URL**: `ws://api.localhost.tiangolo.com/api/v1`

## 认证

所有API请求都需要Bearer Token认证：
```
Authorization: Bearer <access_token>
```

### 获取访问令牌

**POST** `/login/access-token`

```json
// 请求体 (form-data)
{
  "username": "<EMAIL>",
  "password": "password123"
}

// 响应
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

## 用户管理

### 用户注册
**POST** `/users/signup`

```json
// 请求体
{
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "用户姓名"
}

// 响应
{
  "id": "uuid",
  "email": "<EMAIL>",
  "full_name": "用户姓名",
  "is_active": true,
  "is_superuser": false
}
```

### 获取当前用户信息
**GET** `/users/me`

```json
// 响应
{
  "id": "uuid",
  "email": "<EMAIL>",
  "full_name": "用户姓名",
  "is_active": true,
  "is_superuser": false
}
```

## 主题管理

### 创建主题
**POST** `/topics/`

```json
// 请求体
{
  "name": "深度学习基础",
  "description": "学习深度学习的基本概念",
  "category": "AI/ML",
  "difficulty_level": 3
}

// 响应
{
  "id": "uuid",
  "name": "深度学习基础",
  "description": "学习深度学习的基本概念",
  "category": "AI/ML",
  "difficulty_level": 3,
  "owner_id": "uuid",
  "created_at": "2025-08-15T06:00:00Z"
}
```

### 获取主题列表
**GET** `/topics/?skip=0&limit=100`

```json
// 响应
{
  "data": [
    {
      "id": "uuid",
      "name": "深度学习基础",
      "description": "学习深度学习的基本概念",
      "category": "AI/ML",
      "difficulty_level": 3,
      "owner_id": "uuid",
      "created_at": "2025-08-15T06:00:00Z"
    }
  ],
  "count": 1
}
```

### 获取单个主题
**GET** `/topics/{topic_id}`

## 文档管理

### 上传文档
**POST** `/documents/`

```json
// 请求体
{
  "title": "深度学习教程",
  "content": "文档内容...",
  "file_type": "md",
  "size": 1024
}

// 响应
{
  "id": "uuid",
  "title": "深度学习教程",
  "content": "文档内容...",
  "file_type": "md",
  "size": 1024,
  "owner_id": "uuid",
  "created_at": "2025-08-15T06:00:00Z",
  "updated_at": "2025-08-15T06:00:00Z"
}
```

### 获取文档列表
**GET** `/documents/?skip=0&limit=100`

### 获取单个文档
**GET** `/documents/{document_id}`

### 处理文档（分割成块）
**POST** `/documents/{document_id}/process`

```json
// 响应
{
  "message": "Document processing started"
}
```

## 对话管理

### 创建对话
**POST** `/conversations/`

```json
// 请求体
{
  "title": "学习对话",
  "description": "关于深度学习的对话",
  "topic_id": "uuid"  // 可选
}

// 响应
{
  "id": "uuid",
  "title": "学习对话",
  "description": "关于深度学习的对话",
  "status": "active",
  "owner_id": "uuid",
  "topic_id": "uuid",
  "created_at": "2025-08-15T06:00:00Z"
}
```

### 发送消息（AI对话）
**POST** `/conversations/chat`

```json
// 请求体
{
  "message": "请解释什么是深度学习？",
  "conversation_id": "uuid",
  "use_context": true,
  "temperature": 0.7
}

// 响应
{
  "message": "深度学习是机器学习的一个分支...",
  "conversation_id": "uuid",
  "context_used": [
    {
      "content": "相关上下文内容",
      "source": "文档来源",
      "score": 0.95
    }
  ],
  "timestamp": "2025-08-15T06:00:00Z"
}
```

### 获取对话历史
**GET** `/conversations/{conversation_id}/messages?skip=0&limit=50`

```json
// 响应
{
  "data": [
    {
      "id": "uuid",
      "content": "请解释什么是深度学习？",
      "role": "user",
      "conversation_id": "uuid",
      "created_at": "2025-08-15T06:00:00Z"
    },
    {
      "id": "uuid",
      "content": "深度学习是机器学习的一个分支...",
      "role": "assistant",
      "conversation_id": "uuid",
      "created_at": "2025-08-15T06:00:00Z"
    }
  ],
  "count": 2
}
```

### 生成对话摘要
**POST** `/conversations/{conversation_id}/summary?force_regenerate=false`

```json
// 无需请求体

// 响应
{
  "summary": {
    "user_summary": "用户学习了深度学习的基本概念",
    "assistant_summary": "AI解释了深度学习的定义和应用"
  },
  "conversation_id": "uuid",
  "generated_at": "2025-08-15T06:00:00Z"
}
```

## WebSocket 实时对话

### 连接WebSocket
**WebSocket** `/conversations/ws/{conversation_id}`

```javascript
// 连接
const ws = new WebSocket('ws://api.localhost.tiangolo.com/api/v1/conversations/ws/uuid');

// 发送消息
ws.send(JSON.stringify({
  "message": "你好，AI！",
  "timestamp": "2025-08-15T06:00:00Z"
}));

// 接收消息
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('收到消息:', data);
};
```

### WebSocket消息格式

**连接确认**:
```json
{
  "type": "connection_established",
  "conversation_id": "uuid",
  "message": "WebSocket连接已建立"
}
```

**处理状态**:
```json
{
  "type": "processing",
  "message": "正在处理您的消息..."
}
```

**AI回复**:
```json
{
  "type": "ai_response",
  "conversation_id": "uuid",
  "message": "AI的回复内容",
  "timestamp": "2025-08-15T06:00:00Z"
}
```

**错误消息**:
```json
{
  "type": "error",
  "message": "错误描述"
}
```

## 搜索功能

### 语义搜索
**POST** `/search/semantic`

```json
// 请求体
{
  "query": "深度学习的应用",
  "limit": 10,
  "threshold": 0.7
}

// 响应
{
  "results": [
    {
      "content": "匹配的文档内容",
      "source": "文档来源",
      "score": 0.95,
      "chunk_id": "uuid"
    }
  ],
  "total": 1
}
```

## 错误处理

所有API都使用标准HTTP状态码：

- `200`: 成功
- `201`: 创建成功
- `400`: 请求错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 验证错误
- `500`: 服务器错误

错误响应格式：
```json
{
  "detail": "错误描述"
}
```

## 前端集成示例

### React Hook示例

```javascript
// useAuth.js
export const useAuth = () => {
  const [token, setToken] = useState(localStorage.getItem('token'));
  
  const login = async (email, password) => {
    const response = await fetch('/api/v1/login/access-token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({ username: email, password })
    });
    const data = await response.json();
    setToken(data.access_token);
    localStorage.setItem('token', data.access_token);
  };
  
  return { token, login };
};

// useConversation.js
export const useConversation = (conversationId) => {
  const { token } = useAuth();
  const [messages, setMessages] = useState([]);
  
  const sendMessage = async (message) => {
    const response = await fetch('/api/v1/conversations/chat', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message,
        conversation_id: conversationId,
        use_context: true
      })
    });
    return response.json();
  };
  
  return { messages, sendMessage };
};
```
