# API 文档

Master-Know 系统提供完整的 RESTful API，支持文档管理、搜索、用户认证等功能。

## 基础信息

- **Base URL**: `http://api.localhost.tiangolo.com`
- **API 版本**: v1
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`

## 在线文档

访问 [Swagger UI](http://api.localhost.tiangolo.com/docs) 查看完整的交互式 API 文档。

## 认证

### 获取访问令牌

```http
POST /api/v1/login/access-token
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=changethis
```

**响应示例**:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### 使用令牌

在后续请求中添加 Authorization 头：
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 核心 API 端点

### 1. 系统健康检查

```http
GET /api/v1/utils/health-check/
```

**响应**: `true` (系统正常)

### 2. 用户管理

#### 获取当前用户信息
```http
GET /api/v1/users/me
Authorization: Bearer {token}
```

#### 创建新用户
```http
POST /api/v1/users/
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "newpassword",
  "full_name": "User Name"
}
```

### 3. 文档管理

#### 创建文档
```http
POST /api/v1/documents/
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "文档标题",
  "content": "文档内容...",
  "description": "文档描述",
  "file_type": "text/markdown",
  "size": 1024
}
```

**响应示例**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "文档标题",
  "description": "文档描述",
  "created_at": "2025-08-15T10:30:00Z",
  "updated_at": "2025-08-15T10:30:00Z",
  "owner_id": "user-id"
}
```

#### 获取文档列表
```http
GET /api/v1/documents/?skip=0&limit=20
Authorization: Bearer {token}
```

#### 获取单个文档
```http
GET /api/v1/documents/{document_id}
Authorization: Bearer {token}
```

#### 更新文档
```http
PUT /api/v1/documents/{document_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "更新后的标题",
  "content": "更新后的内容..."
}
```

#### 删除文档
```http
DELETE /api/v1/documents/{document_id}
Authorization: Bearer {token}
```

### 4. 文档处理

#### 触发文档处理
```http
POST /api/v1/documents/{document_id}/process
Authorization: Bearer {token}
```

**说明**: 异步处理文档，包括文本分割、向量化等步骤。

#### 获取文档块
```http
GET /api/v1/documents/{document_id}/chunks
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "count": 5,
  "data": [
    {
      "id": "chunk-id-1",
      "content": "文档块内容...",
      "chunk_index": 0,
      "start_char": 0,
      "end_char": 500
    }
  ]
}
```

### 5. 搜索功能

#### 文档搜索 (POST)
```http
POST /api/v1/search/documents
Authorization: Bearer {token}
Content-Type: application/json

{
  "query": "搜索关键词",
  "search_type": "hybrid",
  "limit": 10,
  "offset": 0
}
```

**参数说明**:
- `query`: 搜索查询文本
- `search_type`: 搜索类型 ("text", "vector", "hybrid")
- `limit`: 返回结果数量限制 (1-100)
- `offset`: 跳过的结果数量

#### 文档搜索 (GET)
```http
GET /api/v1/search/documents?q=搜索关键词&search_type=hybrid&limit=10&offset=0
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "hits": [
    {
      "document_id": "doc-id-1",
      "chunk_id": "chunk-id-1",
      "content": "匹配的文档内容...",
      "score": 0.95,
      "highlights": ["关键词"]
    }
  ],
  "total": 1,
  "took": 50,
  "timed_out": false,
  "error": null
}
```

#### 语义搜索
```http
POST /api/v1/search/semantic
Authorization: Bearer {token}
Content-Type: application/json

{
  "query": "深度学习的应用",
  "limit": 10,
  "threshold": 0.7
}
```

#### 搜索服务初始化 (管理员)
```http
POST /api/v1/search/initialize
Authorization: Bearer {admin_token}
```

### 6. 主题管理

#### 创建主题
```http
POST /api/v1/topics/
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "深度学习",
  "description": "深度学习相关的知识和讨论",
  "tags": ["AI", "机器学习"]
}
```

#### 获取主题列表
```http
GET /api/v1/topics/?skip=0&limit=100
Authorization: Bearer {token}
```

#### 获取单个主题
```http
GET /api/v1/topics/{topic_id}
Authorization: Bearer {token}
```

#### 更新主题
```http
PUT /api/v1/topics/{topic_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "更新后的主题名",
  "description": "更新后的描述"
}
```

#### 删除主题
```http
DELETE /api/v1/topics/{topic_id}
Authorization: Bearer {token}
```

#### 获取主题统计
```http
GET /api/v1/topics/{topic_id}/stats
Authorization: Bearer {token}
```

#### 关联文档到主题
```http
POST /api/v1/topics/{topic_id}/documents/{document_id}
Authorization: Bearer {token}
```

### 7. 对话管理

#### 创建对话
```http
POST /api/v1/conversations/
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "学习对话",
  "description": "关于深度学习的对话",
  "topic_id": "uuid"
}
```

#### 获取对话列表
```http
GET /api/v1/conversations/?skip=0&limit=100
Authorization: Bearer {token}
```

#### 获取单个对话
```http
GET /api/v1/conversations/{conversation_id}
Authorization: Bearer {token}
```

#### 获取对话消息
```http
GET /api/v1/conversations/{conversation_id}/messages?skip=0&limit=100
Authorization: Bearer {token}
```

#### 智能聊天
```http
POST /api/v1/conversations/chat
Authorization: Bearer {token}
Content-Type: application/json

{
  "message": "什么是深度学习？",
  "conversation_id": "uuid",
  "topic_id": "uuid",
  "max_tokens": 2048,
  "temperature": 0.7,
  "use_context": true
}
```

**响应示例**:
```json
{
  "message": "深度学习是机器学习的一个分支...",
  "conversation_id": "uuid",
  "message_id": "uuid",
  "context_used": [
    {
      "type": "document",
      "content": "相关文档内容",
      "source": "文档来源",
      "score": 0.95
    }
  ],
  "summary_generated": false
}
```

#### WebSocket 实时对话
```javascript
// 连接WebSocket
const ws = new WebSocket('ws://api.localhost.tiangolo.com/api/v1/conversations/ws/{conversation_id}');

// 发送消息
ws.send(JSON.stringify({
  "message": "你好，AI！",
  "timestamp": "2025-08-15T06:00:00Z"
}));

// 接收消息
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('收到消息:', data);
};
```

### 8. 向量化服务

#### 文本向量化
```http
POST /api/v1/embedding/embed
Authorization: Bearer {token}
Content-Type: application/json

{
  "text": "要向量化的文本内容"
}
```

#### 批量向量化
```http
POST /api/v1/embedding/embed/batch
Authorization: Bearer {token}
Content-Type: application/json

{
  "texts": ["文本1", "文本2", "文本3"]
}
```

#### 向量化服务健康检查
```http
GET /api/v1/embedding/health
Authorization: Bearer {token}
```

### 9. LLM 服务

#### LLM 对话
```http
POST /api/v1/llm/chat
Authorization: Bearer {token}
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "你好"}
  ],
  "model": "gpt-3.5-turbo",
  "max_tokens": 2048,
  "temperature": 0.7
}
```

#### LLM 服务健康检查
```http
GET /api/v1/llm/health
Authorization: Bearer {token}
```

## 错误处理

API 使用标准 HTTP 状态码：

- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证错误
- `500` - 服务器内部错误

**错误响应格式**:
```json
{
  "detail": "错误描述信息"
}
```

**验证错误响应**:
```json
{
  "detail": [
    {
      "loc": ["body", "title"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 10. 用户管理

#### 用户注册
```http
POST /api/v1/users/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "full_name": "用户姓名"
}
```

#### 获取用户列表 (管理员)
```http
GET /api/v1/users/?skip=0&limit=100
Authorization: Bearer {admin_token}
```

#### 获取当前用户信息
```http
GET /api/v1/users/me
Authorization: Bearer {token}
```

#### 更新当前用户信息
```http
PATCH /api/v1/users/me
Authorization: Bearer {token}
Content-Type: application/json

{
  "full_name": "新的用户名",
  "email": "<EMAIL>"
}
```

#### 修改密码
```http
PATCH /api/v1/users/me/password
Authorization: Bearer {token}
Content-Type: application/json

{
  "current_password": "old_password",
  "new_password": "new_password"
}
```

### 11. 系统工具

#### 健康检查
```http
GET /api/v1/utils/health-check/
```

#### 发送测试邮件 (管理员)
```http
POST /api/v1/utils/test-email/
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "email_to": "<EMAIL>"
}
```

### 12. 集成测试

#### Manticore 搜索测试
```http
POST /api/v1/integration-test/manticore/search
Authorization: Bearer {token}
Content-Type: application/json

{
  "query": "测试查询",
  "table_name": "test_table",
  "limit": 10
}
```

#### Dramatiq 任务测试
```http
POST /api/v1/integration-test/dramatiq/hello
Authorization: Bearer {token}
```

## 速率限制

- 每个用户每分钟最多 100 个请求
- 搜索 API 每分钟最多 20 个请求
- 文档处理 API 每分钟最多 10 个请求
- WebSocket 连接每个用户最多 5 个并发连接

## 示例代码

### Python 示例

```python
import httpx
import asyncio

async def main():
    base_url = "http://api.localhost.tiangolo.com"
    
    async with httpx.AsyncClient() as client:
        # 登录获取令牌
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        response = await client.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        token = response.json()["access_token"]
        
        # 使用令牌访问 API
        headers = {"Authorization": f"Bearer {token}"}
        
        # 创建文档
        doc_data = {
            "title": "测试文档",
            "content": "这是一个测试文档的内容",
            "description": "测试用途",
            "file_type": "text/plain",
            "size": 100
        }
        response = await client.post(
            f"{base_url}/api/v1/documents/",
            json=doc_data,
            headers=headers
        )
        document = response.json()
        print(f"创建文档: {document['id']}")
        
        # 搜索文档
        search_data = {
            "query": "测试",
            "search_type": "hybrid",
            "limit": 5,
            "offset": 0
        }
        response = await client.post(
            f"{base_url}/api/v1/search/documents",
            json=search_data,
            headers=headers
        )
        results = response.json()
        print(f"搜索结果: {len(results['hits'])} 个")

        # 创建主题
        topic_data = {
            "name": "测试主题",
            "description": "这是一个测试主题",
            "tags": ["测试", "API"]
        }
        response = await client.post(
            f"{base_url}/api/v1/topics/",
            json=topic_data,
            headers=headers
        )
        topic = response.json()
        print(f"创建主题: {topic['id']}")

        # 智能聊天
        chat_data = {
            "message": "什么是深度学习？",
            "use_context": True,
            "max_tokens": 1000,
            "temperature": 0.7
        }
        response = await client.post(
            f"{base_url}/api/v1/conversations/chat",
            json=chat_data,
            headers=headers
        )
        chat_result = response.json()
        print(f"AI回复: {chat_result['message'][:100]}...")

if __name__ == "__main__":
    asyncio.run(main())
```

### JavaScript 示例

```javascript
const baseURL = 'http://api.localhost.tiangolo.com';

// 登录获取令牌
async function login() {
    const response = await fetch(`${baseURL}/api/v1/login/access-token`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'username=<EMAIL>&password=changethis'
    });
    const data = await response.json();
    return data.access_token;
}

// 创建文档
async function createDocument(token) {
    const response = await fetch(`${baseURL}/api/v1/documents/`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            title: '测试文档',
            content: '这是一个测试文档的内容',
            description: '测试用途',
            file_type: 'text/plain',
            size: 100
        })
    });
    return await response.json();
}

// 搜索文档
async function searchDocuments(token, query) {
    const response = await fetch(`${baseURL}/api/v1/search/documents`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query: query,
            search_type: 'hybrid',
            limit: 10,
            offset: 0
        })
    });
    return await response.json();
}

// 创建主题
async function createTopic(token) {
    const response = await fetch(`${baseURL}/api/v1/topics/`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: '测试主题',
            description: '这是一个测试主题',
            tags: ['测试', 'API']
        })
    });
    return await response.json();
}

// 智能聊天
async function chat(token, message) {
    const response = await fetch(`${baseURL}/api/v1/conversations/chat`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: message,
            use_context: true,
            max_tokens: 1000,
            temperature: 0.7
        })
    });
    return await response.json();
}

// 使用示例
async function main() {
    const token = await login();

    // 创建文档
    const document = await createDocument(token);
    console.log('创建文档:', document.id);

    // 创建主题
    const topic = await createTopic(token);
    console.log('创建主题:', topic.id);

    // 搜索文档
    const searchResults = await searchDocuments(token, '测试');
    console.log('搜索结果:', searchResults.hits.length);

    // 智能聊天
    const chatResult = await chat(token, '什么是深度学习？');
    console.log('AI回复:', chatResult.message);
}
```

## 测试工具

项目提供了完整的测试脚本：

```bash
# 运行完整的系统集成测试
python3 backend/system_integration_test.py

# 运行简化的系统测试
python3 backend/simplified_system_test.py

# 运行API路由测试
python3 backend/test_api_routes.py

# 运行文档服务测试
python3 backend/test_document_service.py

# 运行嵌入服务集成测试
python3 backend/test_embedding_integration.py

# 运行LLM集成测试
python3 backend/test_llm_integration.py

# 运行Manticore集成测试
python3 backend/test_manticore_integration.py

# 运行所有功能测试
python3 backend/test_all_features.py
```

### 在线API文档

访问以下地址查看交互式API文档：

- **Swagger UI**: http://api.localhost.tiangolo.com/docs
- **ReDoc**: http://api.localhost.tiangolo.com/redoc
- **OpenAPI JSON**: http://api.localhost.tiangolo.com/openapi.json

### 测试环境配置

确保以下服务正在运行：

```bash
# 启动所有服务
docker compose up -d

# 检查服务状态
docker compose ps

# 查看服务日志
docker compose logs -f backend
```

## 相关文档

- [部署指南](./DEPLOYMENT.md) - 了解如何部署系统
- [故障排除](./TROUBLESHOOTING.md) - 解决常见问题
- [开发指南](./DEVELOPMENT.md) - 进行二次开发
