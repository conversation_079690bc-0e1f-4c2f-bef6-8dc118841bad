# 故障排除指南

本文档记录了 Master-Know 系统常见问题的解决方案。

## 网络连接问题

### 问题：Backend API 无法访问 (已解决)

**症状**：
- 访问 `http://localhost:8000` 时连接超时
- 容器内部可以访问，但主机无法访问
- 端口映射正确但仍然无法连接

**根本原因**：
在 macOS Docker Desktop 环境下，使用 `DOMAIN=localhost` 会导致 Traefik 代理路由问题。

**解决方案**：
1. 修改 `.env` 文件中的域名配置：
   ```bash
   # 错误配置
   DOMAIN=localhost
   
   # 正确配置
   DOMAIN=localhost.tiangolo.com
   ```

2. 重启服务：
   ```bash
   docker compose down
   docker compose up -d
   ```

3. 使用正确的 API 端点：
   - ❌ `http://localhost:8000`
   - ✅ `http://api.localhost.tiangolo.com`

**验证修复**：
```bash
curl http://api.localhost.tiangolo.com/api/v1/utils/health-check/
# 应该返回: true
```

### 问题：端口冲突

**症状**：
- Docker 启动时报错 "port already in use"
- 服务无法绑定到指定端口

**解决方案**：
1. 检查端口占用：
   ```bash
   # macOS/Linux
   lsof -i :5173
   lsof -i :8000
   
   # Windows
   netstat -ano | findstr :5173
   ```

2. 停止占用端口的进程或修改配置：
   ```bash
   # 修改 docker-compose.override.yml
   services:
     frontend:
       ports:
         - "5174:5173"  # 使用不同的主机端口
   ```

## 服务启动问题

### 问题：数据库连接失败

**症状**：
- Backend 服务启动失败
- 日志显示 "connection to server failed"

**解决方案**：
1. 检查数据库服务状态：
   ```bash
   docker compose ps db
   ```

2. 查看数据库日志：
   ```bash
   docker compose logs db
   ```

3. 重启数据库服务：
   ```bash
   docker compose restart db
   ```

4. 如果数据损坏，重新初始化：
   ```bash
   docker compose down -v  # 删除数据卷
   docker compose up -d
   ```

### 问题：Dramatiq Worker 配置错误

**症状**：
- 文档处理失败
- Worker 日志显示配置字段不存在

**解决方案**：
1. 检查配置字段名是否正确：
   ```bash
   # 正确的配置字段名
   EMBEDDING_OPENAI_API_KEY=your_key
   EMBEDDING_OPENAI_BASE_URL=https://api.openai.com/v1
   EMBEDDING_DEFAULT_MODEL=text-embedding-3-small
   ```

2. 重新构建并重启 Worker：
   ```bash
   docker compose build backend
   docker compose restart dramatiq-worker
   ```

3. 验证配置：
   ```bash
   docker exec master-know-dramatiq-worker-1 env | grep EMBEDDING
   ```

## 性能问题

### 问题：服务响应缓慢

**症状**：
- API 请求超时
- 页面加载缓慢

**诊断步骤**：
1. 检查资源使用情况：
   ```bash
   docker stats
   ```

2. 查看服务日志：
   ```bash
   docker compose logs -f backend
   ```

3. 检查数据库性能：
   ```bash
   docker exec -it master-know-db-1 psql -U master_know_user master_know
   # 在 psql 中执行
   SELECT * FROM pg_stat_activity;
   ```

**解决方案**：
1. 增加资源限制：
   ```yaml
   # docker-compose.override.yml
   services:
     backend:
       deploy:
         resources:
           limits:
             memory: 2G
             cpus: '1.0'
   ```

2. 优化数据库查询：
   ```sql
   -- 创建索引
   CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
   ```

### 问题：内存不足

**症状**：
- 容器被 OOM Killer 终止
- 系统变得不响应

**解决方案**：
1. 检查内存使用：
   ```bash
   docker stats --no-stream
   ```

2. 减少并发处理：
   ```bash
   # 在 .env 中设置
   DRAMATIQ_WORKERS=2  # 减少 worker 数量
   ```

3. 清理未使用的资源：
   ```bash
   docker system prune -f
   docker volume prune -f
   ```

## 数据问题

### 问题：文档处理失败

**症状**：
- 文档创建成功但无法生成文档块
- 搜索结果为空

**诊断步骤**：
1. 检查 Dramatiq Worker 状态：
   ```bash
   docker compose logs dramatiq-worker --tail 20
   ```

2. 验证嵌入服务配置：
   ```bash
   # 测试 OpenAI API 连接
   curl -H "Authorization: Bearer $EMBEDDING_OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

3. 检查文档处理任务：
   ```bash
   # 手动触发文档处理
   python3 test_document_simple.py
   ```

**解决方案**：
1. 重启处理服务：
   ```bash
   docker compose restart dramatiq-worker
   ```

2. 检查 API 密钥配置：
   ```bash
   # 确保 .env 中有正确的 API 密钥
   EMBEDDING_OPENAI_API_KEY=sk-...
   ```

### 问题：搜索功能异常

**症状**：
- 搜索返回 500 错误
- Manticore Search 连接失败

**解决方案**：
1. 检查 Manticore 服务：
   ```bash
   docker compose logs manticore
   ```

2. 测试 Manticore 连接：
   ```bash
   # 测试 MySQL 协议
   docker exec master-know-manticore-1 mysql -h127.0.0.1 -P9306 -e "SHOW TABLES"
   
   # 测试 HTTP 接口
   curl -X POST "http://localhost:9308/sql" \
        -H "Content-Type: application/json" \
        -d '{"query": "SHOW STATUS"}'
   ```

3. 重建搜索索引：
   ```bash
   docker compose restart manticore
   ```

## 开发环境问题

### 问题：代码热重载不工作

**症状**：
- 修改代码后需要手动重启容器
- `docker compose watch` 不响应文件变化

**解决方案**：
1. 确保使用正确的启动命令：
   ```bash
   docker compose watch  # 而不是 docker compose up
   ```

2. 检查文件同步配置：
   ```yaml
   # docker-compose.yml 中应该有 develop 配置
   services:
     backend:
       develop:
         watch:
           - action: sync
             path: ./backend/app
             target: /app/app
   ```

3. 重启 watch 模式：
   ```bash
   docker compose watch --no-up  # 停止 watch
   docker compose watch          # 重新启动
   ```

### 问题：测试脚本失败

**症状**：
- `test_system_integration.py` 报错
- 依赖库缺失

**解决方案**：
1. 安装测试依赖：
   ```bash
   pip3 install httpx redis psycopg2-binary semantic-text-splitter
   ```

2. 检查服务是否完全启动：
   ```bash
   docker compose ps
   # 确保所有服务状态为 "Up"
   ```

3. 等待服务初始化完成：
   ```bash
   # 等待数据库就绪
   docker compose logs db | grep "ready to accept connections"
   ```

## 日志分析

### 查看特定服务日志

```bash
# Backend 服务日志
docker compose logs -f backend

# 数据库日志
docker compose logs -f db

# Worker 日志
docker compose logs -f dramatiq-worker

# 所有服务日志
docker compose logs -f
```

### 日志级别调整

在 `.env` 文件中设置：
```bash
LOG_LEVEL=DEBUG  # 获取更详细的日志
```

## 获取帮助

如果以上解决方案都无法解决问题：

1. **收集诊断信息**：
   ```bash
   # 系统信息
   docker version
   docker compose version
   
   # 服务状态
   docker compose ps
   
   # 资源使用
   docker stats --no-stream
   
   # 最近日志
   docker compose logs --tail 100 > logs.txt
   ```

2. **运行完整诊断**：
   ```bash
   python3 test_system_integration.py > diagnosis.txt 2>&1
   ```

3. **检查网络配置**：
   ```bash
   docker network ls
   docker network inspect master-know_default
   ```

## 相关文档

- [部署指南](./DEPLOYMENT.md) - 正确的部署流程
- [API 文档](./API.md) - API 使用说明
- [开发指南](./DEVELOPMENT.md) - 开发环境配置
