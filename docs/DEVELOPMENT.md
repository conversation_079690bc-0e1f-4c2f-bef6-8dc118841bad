# 开发指南

本文档详细说明了 Master-Know 系统的开发环境搭建和开发流程。

## 开发环境要求

### 系统要求
- **操作系统**: macOS, Linux, 或 Windows (with WSL2)
- **内存**: 最少 8GB，推荐 16GB
- **存储**: 最少 20GB 可用空间

### 必需软件
- **Docker Desktop** (macOS/Windows) 或 **Docker Engine** (Linux)
- **Git** 2.0+
- **Python** 3.10+
- **Node.js** 18+ (用于前端开发)
- **代码编辑器**: VS Code (推荐) 或其他

### 推荐工具
- **Docker Compose** v2.0+
- **uv** (Python 包管理器)
- **pnpm** (Node.js 包管理器)

## 项目结构

```
master-know/
├── backend/                 # FastAPI 后端
│   ├── app/                # 应用代码
│   │   ├── api/           # API 路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   └── services/      # 业务逻辑
│   ├── scripts/           # 部署脚本
│   └── tests/             # 测试代码
├── frontend/               # React 前端
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   └── package.json       # 依赖配置
├── engines/                # 核心引擎
│   └── text_splitter/     # 文本分割引擎
├── docs/                   # 项目文档
├── docker-compose.yml      # Docker 配置
├── .env.example           # 环境变量模板
└── README.md              # 项目说明
```

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd master-know
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

**关键配置**：
```bash
# 域名配置 (开发环境必须设置)
DOMAIN=localhost.tiangolo.com

# 数据库配置
POSTGRES_PASSWORD=changethis
POSTGRES_USER=master_know_user
POSTGRES_DB=master_know

# API 密钥 (需要真实的 OpenAI API Key)
EMBEDDING_OPENAI_API_KEY=sk-your-openai-api-key-here

# 安全密钥 (生成新的)
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
```

### 3. 启动开发环境

```bash
# 启动所有服务 (支持热重载)
docker compose watch

# 或者使用标准模式
docker compose up -d
```

### 4. 验证环境

```bash
# 安装测试依赖
pip3 install httpx redis psycopg2-binary semantic-text-splitter

# 运行集成测试
python3 test_system_integration.py
```

## 开发工作流

### Backend 开发

#### 1. 进入开发容器

```bash
# 进入 backend 容器
docker exec -it master-know-backend-1 bash

# 或者使用 VS Code Dev Containers 扩展
```

#### 2. 代码结构

```python
# app/api/routes/documents.py - API 路由
from fastapi import APIRouter, Depends
from app.services.document.document_service import DocumentService

router = APIRouter()

@router.post("/")
async def create_document(
    document_data: DocumentCreate,
    service: DocumentService = Depends()
):
    return await service.create_document(document_data)
```

```python
# app/services/document/document_service.py - 业务逻辑
class DocumentService:
    def __init__(self, session: Session):
        self.session = session
    
    async def create_document(self, data: DocumentCreate):
        # 业务逻辑实现
        pass
```

```python
# app/models/document.py - 数据模型
from sqlmodel import SQLModel, Field

class Document(SQLModel, table=True):
    id: str = Field(primary_key=True)
    title: str
    content: str
    created_at: datetime
```

#### 3. 数据库迁移

```bash
# 创建新的迁移
docker exec master-know-backend-1 alembic revision --autogenerate -m "Add new table"

# 应用迁移
docker exec master-know-backend-1 alembic upgrade head

# 查看迁移历史
docker exec master-know-backend-1 alembic history
```

#### 4. 运行测试

```bash
# 运行所有测试
docker exec master-know-backend-1 python -m pytest

# 运行特定测试
docker exec master-know-backend-1 python -m pytest tests/test_documents.py

# 运行测试并生成覆盖率报告
docker exec master-know-backend-1 python -m pytest --cov=app
```

### Frontend 开发

#### 1. 本地开发

```bash
cd frontend

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

#### 2. 代码结构

```typescript
// src/components/DocumentList.tsx
import React from 'react';
import { useDocuments } from '../hooks/useDocuments';

export const DocumentList: React.FC = () => {
  const { documents, loading } = useDocuments();
  
  return (
    <div>
      {documents.map(doc => (
        <div key={doc.id}>{doc.title}</div>
      ))}
    </div>
  );
};
```

```typescript
// src/hooks/useDocuments.ts
import { useQuery } from '@tanstack/react-query';
import { documentsApi } from '../api/documents';

export const useDocuments = () => {
  return useQuery({
    queryKey: ['documents'],
    queryFn: documentsApi.getAll
  });
};
```

#### 3. API 集成

```typescript
// src/api/documents.ts
const BASE_URL = 'http://api.localhost.tiangolo.com';

export const documentsApi = {
  async getAll() {
    const response = await fetch(`${BASE_URL}/api/v1/documents/`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    return response.json();
  },
  
  async create(data: DocumentCreate) {
    const response = await fetch(`${BASE_URL}/api/v1/documents/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }
};
```

### 引擎开发

#### 文本分割引擎

```python
# engines/text_splitter/engine.py
from typing import List
from .config import TextSplitterConfig

class TextSplitterEngine:
    def __init__(self, config: TextSplitterConfig):
        self.config = config
    
    def split_text(self, text: str, strategy: str = "token_based") -> List[str]:
        """分割文本为块"""
        if strategy == "token_based":
            return self._split_by_tokens(text)
        elif strategy == "semantic":
            return self._split_by_semantics(text)
        else:
            raise ValueError(f"Unknown strategy: {strategy}")
    
    def _split_by_tokens(self, text: str) -> List[str]:
        # 实现基于 token 的分割
        pass
```

## 调试技巧

### 1. 日志调试

```python
# 在代码中添加日志
import logging

logger = logging.getLogger(__name__)

def my_function():
    logger.info("Function called")
    logger.debug(f"Processing data: {data}")
    logger.error(f"Error occurred: {error}")
```

```bash
# 查看实时日志
docker compose logs -f backend

# 设置日志级别
# 在 .env 中设置
LOG_LEVEL=DEBUG
```

### 2. 断点调试

```python
# 使用 pdb 进行调试
import pdb; pdb.set_trace()

# 或者使用 ipdb (更友好的界面)
import ipdb; ipdb.set_trace()
```

### 3. 数据库调试

```bash
# 连接到数据库
docker exec -it master-know-db-1 psql -U master_know_user master_know

# 查看表结构
\dt
\d documents

# 执行查询
SELECT * FROM documents LIMIT 5;
```

### 4. API 调试

```bash
# 使用 curl 测试 API
curl -X POST "http://api.localhost.tiangolo.com/api/v1/documents/" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title": "Test", "content": "Content"}'

# 使用 httpie (更友好)
http POST api.localhost.tiangolo.com/api/v1/documents/ \
     Authorization:"Bearer $TOKEN" \
     title="Test" content="Content"
```

## 测试策略

### 1. 单元测试

```python
# tests/test_document_service.py
import pytest
from app.services.document.document_service import DocumentService

class TestDocumentService:
    def test_create_document(self):
        service = DocumentService(mock_session)
        result = service.create_document(test_data)
        assert result.title == test_data.title
```

### 2. 集成测试

```python
# tests/test_api_integration.py
import httpx
import pytest

@pytest.mark.asyncio
async def test_document_creation_flow():
    async with httpx.AsyncClient() as client:
        # 登录
        login_response = await client.post("/api/v1/login/access-token", ...)
        token = login_response.json()["access_token"]
        
        # 创建文档
        doc_response = await client.post(
            "/api/v1/documents/",
            headers={"Authorization": f"Bearer {token}"},
            json={"title": "Test", "content": "Content"}
        )
        assert doc_response.status_code == 200
```

### 3. 端到端测试

```bash
# 运行完整的系统测试
python3 test_system_integration.py

# 运行文档处理测试
python3 test_document_simple.py
```

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_documents_title ON documents USING gin(to_tsvector('english', title));

-- 分析查询性能
EXPLAIN ANALYZE SELECT * FROM documents WHERE title ILIKE '%search%';
```

### 2. API 优化

```python
# 使用异步处理
from fastapi import BackgroundTasks

@router.post("/documents/{doc_id}/process")
async def process_document(doc_id: str, background_tasks: BackgroundTasks):
    background_tasks.add_task(process_document_task, doc_id)
    return {"message": "Processing started"}
```

### 3. 缓存策略

```python
# 使用 Redis 缓存
from app.core.cache import cache

@cache.cached(timeout=300)
async def get_document(doc_id: str):
    return await document_service.get(doc_id)
```

## 部署准备

### 1. 构建生产镜像

```bash
# 构建 backend 镜像
docker build -t master-know-backend:latest ./backend

# 构建 frontend 镜像
docker build -t master-know-frontend:latest ./frontend
```

### 2. 运行生产测试

```bash
# 使用生产配置启动
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 运行性能测试
python3 test_performance.py
```

## 代码规范

### Python 代码规范

```bash
# 代码格式化
docker exec master-know-backend-1 black app/
docker exec master-know-backend-1 isort app/

# 代码检查
docker exec master-know-backend-1 flake8 app/
docker exec master-know-backend-1 mypy app/
```

### TypeScript 代码规范

```bash
cd frontend

# 代码格式化
pnpm format

# 代码检查
pnpm lint

# 类型检查
pnpm type-check
```

## 相关文档

- [部署指南](./DEPLOYMENT.md) - 生产环境部署
- [API 文档](./API.md) - API 接口说明
- [故障排除](./TROUBLESHOOTING.md) - 常见问题解决
