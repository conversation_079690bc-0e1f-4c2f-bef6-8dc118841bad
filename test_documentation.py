#!/usr/bin/env python3
"""
文档验证测试脚本

验证项目文档的准确性和完整性，确保所有配置信息、命令和链接都是正确的。
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import httpx


class DocumentationTester:
    """文档验证测试器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.docs_dir = self.base_dir / "docs"
        self.results = []
        
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅" if success else "❌"
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
        print(f"{status} {test_name}: {message}")
    
    def test_file_existence(self):
        """测试关键文档文件是否存在"""
        print("\n📁 检查文档文件存在性...")
        
        required_files = [
            "docs/DEPLOYMENT.md",
            "docs/API.md", 
            "docs/TROUBLESHOOTING.md",
            "docs/DEVELOPMENT.md",
            "docs/ARCHITECTURE.md",
            "docs/INDEX.md",
            "README.md",
            ".env.example",
            "docker-compose.yml",
            "test_system_integration.py",
            "test_document_simple.py"
        ]
        
        for file_path in required_files:
            full_path = self.base_dir / file_path
            exists = full_path.exists()
            self.log_result(
                f"文件存在: {file_path}",
                exists,
                "存在" if exists else "缺失"
            )
    
    def test_env_configuration(self):
        """测试环境配置"""
        print("\n⚙️ 检查环境配置...")
        
        env_file = self.base_dir / ".env"
        env_example = self.base_dir / ".env.example"
        
        # 检查 .env.example 存在
        if not env_example.exists():
            self.log_result("环境配置模板", False, ".env.example 文件不存在")
            return
        
        # 读取 .env.example 内容
        with open(env_example, 'r') as f:
            env_content = f.read()
        
        # 检查关键配置项
        required_configs = [
            "DOMAIN=",
            "POSTGRES_SERVER=",
            "REDIS_HOST=",
            "EMBEDDING_OPENAI_API_KEY=",
            "SECRET_KEY="
        ]
        
        for config in required_configs:
            exists = config in env_content
            self.log_result(
                f"配置项: {config}",
                exists,
                "已配置" if exists else "缺失"
            )
        
        # 检查域名配置是否正确
        if "DOMAIN=localhost.tiangolo.com" in env_content:
            self.log_result("域名配置", True, "DOMAIN=localhost.tiangolo.com")
        else:
            self.log_result("域名配置", False, "域名配置不正确，应为 localhost.tiangolo.com")
    
    def test_docker_configuration(self):
        """测试 Docker 配置"""
        print("\n🐳 检查 Docker 配置...")
        
        compose_file = self.base_dir / "docker-compose.yml"
        if not compose_file.exists():
            self.log_result("Docker Compose 文件", False, "docker-compose.yml 不存在")
            return
        
        with open(compose_file, 'r') as f:
            compose_content = f.read()
        
        # 检查关键服务
        required_services = [
            "backend:",
            "frontend:",
            "db:",
            "redis:",
            "manticore:",
            "dramatiq-worker:",
            "gateway:"
        ]
        
        for service in required_services:
            exists = service in compose_content
            self.log_result(
                f"Docker 服务: {service.rstrip(':')}",
                exists,
                "已配置" if exists else "缺失"
            )
    
    async def test_api_endpoints(self):
        """测试 API 端点可访问性"""
        print("\n🌐 检查 API 端点...")
        
        base_url = "http://api.localhost.tiangolo.com"
        endpoints = [
            "/api/v1/utils/health-check/",
            "/docs",
            "/api/v1/openapi.json"
        ]
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            for endpoint in endpoints:
                try:
                    response = await client.get(f"{base_url}{endpoint}")
                    success = response.status_code in [200, 307]  # 307 for redirects
                    self.log_result(
                        f"API 端点: {endpoint}",
                        success,
                        f"状态码: {response.status_code}"
                    )
                except Exception as e:
                    self.log_result(
                        f"API 端点: {endpoint}",
                        False,
                        f"连接失败: {str(e)}"
                    )
    
    def test_docker_services(self):
        """测试 Docker 服务状态"""
        print("\n🔧 检查 Docker 服务状态...")
        
        try:
            # 检查 Docker 是否运行
            result = subprocess.run(
                ["docker", "ps"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode != 0:
                self.log_result("Docker 运行状态", False, "Docker 未运行或无权限")
                return
            
            # 检查项目容器
            result = subprocess.run(
                ["docker", "compose", "ps", "--format", "json"],
                cwd=self.base_dir,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.log_result("Docker Compose 状态", True, "可以获取服务状态")
                
                # 解析服务状态
                if result.stdout.strip():
                    import json
                    try:
                        services = [json.loads(line) for line in result.stdout.strip().split('\n')]
                        running_services = [s for s in services if s.get('State') == 'running']
                        self.log_result(
                            "运行中的服务",
                            len(running_services) > 0,
                            f"发现 {len(running_services)} 个运行中的服务"
                        )
                    except json.JSONDecodeError:
                        self.log_result("服务状态解析", False, "无法解析服务状态")
                else:
                    self.log_result("服务状态", False, "没有发现运行中的服务")
            else:
                self.log_result("Docker Compose 状态", False, f"命令失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            self.log_result("Docker 检查", False, "命令超时")
        except FileNotFoundError:
            self.log_result("Docker 检查", False, "Docker 命令未找到")
        except Exception as e:
            self.log_result("Docker 检查", False, f"检查失败: {str(e)}")
    
    def test_test_scripts(self):
        """测试测试脚本的可执行性"""
        print("\n🧪 检查测试脚本...")
        
        test_scripts = [
            "test_system_integration.py",
            "test_document_simple.py"
        ]
        
        for script in test_scripts:
            script_path = self.base_dir / script
            if script_path.exists():
                # 检查是否可执行
                try:
                    result = subprocess.run(
                        [sys.executable, "-m", "py_compile", str(script_path)],
                        capture_output=True,
                        timeout=10
                    )
                    success = result.returncode == 0
                    self.log_result(
                        f"脚本语法: {script}",
                        success,
                        "语法正确" if success else f"语法错误: {result.stderr.decode()}"
                    )
                except Exception as e:
                    self.log_result(f"脚本检查: {script}", False, f"检查失败: {str(e)}")
            else:
                self.log_result(f"脚本存在: {script}", False, "文件不存在")
    
    def test_documentation_links(self):
        """测试文档内部链接"""
        print("\n🔗 检查文档链接...")
        
        # 检查主要文档文件中的内部链接
        doc_files = [
            "docs/INDEX.md",
            "docs/DEPLOYMENT.md",
            "docs/API.md",
            "README.md"
        ]
        
        for doc_file in doc_files:
            doc_path = self.base_dir / doc_file
            if not doc_path.exists():
                self.log_result(f"文档链接检查: {doc_file}", False, "文件不存在")
                continue
            
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查相对链接
            import re
            links = re.findall(r'\[.*?\]\((\.\/.*?\.md)\)', content)
            
            broken_links = []
            for link in links:
                # 移除 ./ 前缀并解析相对路径
                link_path = doc_path.parent / link[2:]  # 移除 ./
                if not link_path.exists():
                    broken_links.append(link)
            
            if broken_links:
                self.log_result(
                    f"文档链接: {doc_file}",
                    False,
                    f"发现 {len(broken_links)} 个失效链接: {broken_links[:3]}"
                )
            else:
                self.log_result(
                    f"文档链接: {doc_file}",
                    True,
                    f"检查了 {len(links)} 个链接，全部有效"
                )
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🔍 开始文档验证测试...\n")
        
        # 运行各项测试
        self.test_file_existence()
        self.test_env_configuration()
        self.test_docker_configuration()
        self.test_docker_services()
        self.test_test_scripts()
        self.test_documentation_links()
        await self.test_api_endpoints()
        
        # 汇总结果
        print("\n" + "="*60)
        print("📊 测试结果汇总")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n" + "="*60)
        
        if failed_tests == 0:
            print("🎉 所有文档验证测试通过！文档系统完全正常。")
            return True
        else:
            print("⚠️  部分测试失败，请检查上述问题。")
            return False


async def main():
    """主函数"""
    tester = DocumentationTester()
    success = await tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
