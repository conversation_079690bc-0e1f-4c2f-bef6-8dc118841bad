#!/usr/bin/env python3
"""
简化的文档处理测试
"""

import asyncio
import httpx
import json

async def test_document_workflow():
    """测试文档工作流程"""
    base_url = "http://api.localhost.tiangolo.com"
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        # 1. 认证
        print("🔐 用户认证...")
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        response = await client.post(
            f"{base_url}/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code != 200:
            print(f"❌ 认证失败: {response.status_code}")
            return
            
        token = response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        print("✅ 认证成功")
        
        # 2. 创建文档
        print("📄 创建文档...")
        content = "这是一个测试文档。它包含人工智能相关的内容。深度学习是AI的重要分支。"
        
        doc_data = {
            "title": "AI测试文档",
            "content": content,
            "description": "测试文档",
            "file_type": "text/plain",
            "size": len(content.encode('utf-8'))
        }
        
        response = await client.post(
            f"{base_url}/api/v1/documents/",
            json=doc_data,
            headers={**headers, "Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ 文档创建失败: {response.status_code} - {response.text}")
            return
            
        doc_id = response.json()["id"]
        print(f"✅ 文档创建成功: {doc_id}")
        
        # 3. 触发处理
        print("⚙️  触发文档处理...")
        response = await client.post(
            f"{base_url}/api/v1/documents/{doc_id}/process",
            headers=headers
        )
        
        if response.status_code == 200:
            print("✅ 处理任务已启动")
        else:
            print(f"⚠️  处理启动失败: {response.status_code}")
        
        # 4. 等待并检查块
        print("🔍 检查文档块...")
        for i in range(10):
            await asyncio.sleep(2)
            
            response = await client.get(
                f"{base_url}/api/v1/documents/{doc_id}/chunks",
                headers=headers
            )
            
            if response.status_code == 200:
                chunks = response.json()
                count = chunks.get("count", 0)
                print(f"   尝试 {i+1}: 找到 {count} 个块")
                
                if count > 0:
                    print(f"✅ 文档处理成功，生成 {count} 个块")
                    # 显示第一个块的内容
                    if chunks.get("data"):
                        first_chunk = chunks["data"][0]
                        print(f"   第一个块: {first_chunk['content'][:50]}...")
                    break
            else:
                print(f"   获取块失败: {response.status_code}")
        else:
            print("⏰ 处理超时")
        
        # 5. 测试搜索
        print("🔍 测试搜索...")
        search_data = {"query": "人工智能", "limit": 5}
        
        response = await client.post(
            f"{base_url}/api/v1/search/documents",
            json=search_data,
            headers={**headers, "Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            results = response.json()
            hit_count = len(results.get("hits", []))
            print(f"✅ 搜索成功，找到 {hit_count} 个结果")
        else:
            print(f"❌ 搜索失败: {response.status_code}")

if __name__ == "__main__":
    asyncio.run(test_document_workflow())
