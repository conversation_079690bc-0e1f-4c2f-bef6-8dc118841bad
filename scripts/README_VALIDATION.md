# 知深学习导师 - 功能验证测试指南

本目录包含了完整的功能验证测试脚本，用于验证"知深学习导师"项目的各个模块和功能实现。

## 📋 测试脚本概览

### 🚀 快速开始

```bash
# 运行所有验证测试
python scripts/run_all_tests.py

# 只运行快速验证
python scripts/run_all_tests.py --type quick

# 只运行用户故事验证
python scripts/run_all_tests.py --type user_stories

# 只运行端到端测试
python scripts/run_all_tests.py --type e2e
```

### 📁 脚本文件说明

| 脚本文件 | 描述 | 用途 |
|---------|------|------|
| `run_all_tests.py` | 🎯 **主测试运行器** | 统一入口，运行所有验证测试 |
| `quick_validation.py` | ⚡ **快速验证** | 检查服务状态和基本功能 |
| `validate_user_stories.py` | 📖 **用户故事验证** | 基于用户故事验证各模块功能 |
| `e2e_learning_flow_test.py` | 🔄 **端到端测试** | 验证完整的学习流程 |

## 🎯 验证范围

### ✅ 已验证功能模块

- **用户认证与管理** - 注册、登录、JWT认证
- **文档处理与存储** - 上传、解析、分块、存储
- **主题管理** - 创建、编辑、关联文档
- **文本分割引擎** - 智能语义分割
- **搜索引擎集成** - Manticore混合搜索
- **异步任务处理** - Dramatiq队列系统
- **API网关** - BFF架构和路由

### 🔄 待验证功能

- **对话管理** - AI对话交互
- **摘要生成** - 动态学习摘要
- **实时通信** - WebSocket支持
- **前端集成** - React UI界面

## 🛠️ 使用前准备

### 1. 环境要求

```bash
# Python 3.8+
python --version

# 安装依赖
pip install httpx asyncio
```

### 2. 启动服务

```bash
# 启动所有Docker服务
docker compose up -d

# 检查服务状态
docker compose ps
```

### 3. 验证服务可用性

```bash
# 快速检查所有服务
python scripts/quick_validation.py
```

## 📊 测试详情

### 🔍 快速验证 (quick_validation.py)

**目的**: 快速检查系统基本状态  
**耗时**: ~30秒  
**检查项目**:
- 服务连接状态
- API端点可用性
- 数据库连接
- 外部服务集成

**使用场景**:
- 系统部署后的初始检查
- 日常健康状态监控
- 故障排除的第一步

### 📖 用户故事验证 (validate_user_stories.py)

**目的**: 基于用户故事验证功能实现  
**耗时**: ~2-3分钟  
**验证内容**:
- US-001: 用户注册与登录
- US-002: 文档上传与处理
- US-003: 学习主题创建
- US-004: 文档智能分割
- US-005: 文本向量化处理
- US-006: 智能上下文检索

**使用场景**:
- 功能开发完成后的验证
- 版本发布前的质量检查
- 回归测试

### 🔄 端到端测试 (e2e_learning_flow_test.py)

**目的**: 验证完整的用户学习流程  
**耗时**: ~3-5分钟  
**测试流程**:
1. 用户注册和登录
2. 创建学习主题
3. 上传学习文档
4. 测试文档搜索
5. 测试嵌入服务
6. 测试集成功能

**使用场景**:
- 系统集成测试
- 用户验收测试
- 性能基准测试

## 📈 结果解读

### ✅ 成功指标

- **100% 通过率**: 所有功能正常，系统可用
- **80-99% 通过率**: 基本可用，有少量问题
- **50-79% 通过率**: 部分功能异常，需要检查
- **<50% 通过率**: 系统存在严重问题

### 📋 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 连接超时 | 服务未启动 | `docker compose up -d` |
| 认证失败 | JWT配置问题 | 检查环境变量配置 |
| 数据库错误 | PostgreSQL连接问题 | 检查数据库服务状态 |
| 搜索失败 | Manticore未就绪 | 等待服务完全启动 |

## 🔧 故障排除

### 1. 检查服务状态

```bash
# 查看所有服务状态
docker compose ps

# 查看特定服务日志
docker compose logs backend
docker compose logs manticore
docker compose logs redis
```

### 2. 重启服务

```bash
# 重启所有服务
docker compose restart

# 重启特定服务
docker compose restart backend
```

### 3. 清理和重建

```bash
# 停止所有服务
docker compose down

# 清理数据卷（谨慎使用）
docker compose down -v

# 重新构建和启动
docker compose up -d --build
```

## 📝 测试报告

测试完成后，会生成以下文件：

- `test_results.json` - 详细的测试结果数据
- 控制台输出 - 实时测试进度和结果

## 🎯 最佳实践

1. **定期运行**: 建议每次代码变更后运行快速验证
2. **完整测试**: 重要版本发布前运行完整测试套件
3. **监控日志**: 关注测试过程中的警告和错误信息
4. **环境隔离**: 在独立的测试环境中运行验证
5. **结果记录**: 保存测试结果用于趋势分析

## 🚀 下一步

验证测试通过后，可以：

1. **用户验收测试** - 邀请真实用户测试
2. **性能优化** - 基于测试结果优化系统性能
3. **功能扩展** - 开发更多高级功能
4. **生产部署** - 部署到生产环境

---

**注意**: 这些测试脚本会创建测试数据，建议在开发或测试环境中运行，避免影响生产数据。
