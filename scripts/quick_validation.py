#!/usr/bin/env python3
"""
快速验证脚本

快速检查知深学习导师项目的各个服务和功能模块状态。
"""

import asyncio
import httpx
import json
from typing import Dict, List
import time

# 服务配置
SERVICES = {
    "主后端服务": "http://localhost:8000",
    "主题服务": "http://localhost:9004",
    "网关服务": "http://localhost:8080",
}

API_ENDPOINTS = {
    "健康检查": "/api/v1/utils/health-check/",
    "用户登录": "/api/v1/login/access-token",
    "文档API": "/api/v1/documents/",
    "搜索API": "/api/v1/search/health",
    "嵌入API": "/api/v1/embedding/health",
    "集成测试": "/api/v1/integration/test/full-integration",
}

class QuickValidator:
    """快速验证器"""
    
    def __init__(self):
        self.results = []
    
    def log_result(self, service: str, endpoint: str, success: bool, details: str = ""):
        """记录验证结果"""
        result = {
            "service": service,
            "endpoint": endpoint,
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {service} - {endpoint}: {details}")
    
    async def check_service_health(self, service_name: str, base_url: str):
        """检查服务健康状态"""
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # 基本连接测试
                response = await client.get(f"{base_url}/")
                if response.status_code in [200, 404]:  # 404也表示服务在运行
                    self.log_result(service_name, "基本连接", True, f"状态码: {response.status_code}")
                else:
                    self.log_result(service_name, "基本连接", False, f"状态码: {response.status_code}")
                    return False
                
                # 健康检查
                try:
                    health_response = await client.get(f"{base_url}/api/v1/utils/health-check")
                    if health_response.status_code == 200:
                        health_data = health_response.json()
                        self.log_result(service_name, "健康检查", True, f"状态: {health_data.get('message', 'OK')}")
                    else:
                        self.log_result(service_name, "健康检查", False, f"状态码: {health_response.status_code}")
                except:
                    self.log_result(service_name, "健康检查", False, "健康检查端点不可用")
                
                # API文档检查
                try:
                    docs_response = await client.get(f"{base_url}/docs")
                    if docs_response.status_code == 200:
                        self.log_result(service_name, "API文档", True, "文档可访问")
                    else:
                        self.log_result(service_name, "API文档", False, f"状态码: {docs_response.status_code}")
                except:
                    self.log_result(service_name, "API文档", False, "文档不可访问")
                
                return True
                
            except httpx.ConnectError:
                self.log_result(service_name, "连接", False, "服务不可达")
                return False
            except Exception as e:
                self.log_result(service_name, "连接", False, str(e))
                return False
    
    async def check_api_endpoints(self):
        """检查主要API端点"""
        base_url = SERVICES["主后端服务"]
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            for endpoint_name, endpoint_path in API_ENDPOINTS.items():
                try:
                    if endpoint_name == "用户登录":
                        # POST请求测试
                        response = await client.post(f"{base_url}{endpoint_path}")
                        # 422是预期的，因为没有提供登录数据
                        if response.status_code in [422, 400]:
                            self.log_result("API端点", endpoint_name, True, "端点可用")
                        else:
                            self.log_result("API端点", endpoint_name, False, f"状态码: {response.status_code}")
                    
                    elif endpoint_name in ["文档API", "搜索API", "嵌入API"]:
                        # GET请求测试（可能需要认证）
                        response = await client.get(f"{base_url}{endpoint_path}")
                        if response.status_code in [200, 401, 422]:  # 401表示需要认证，也是正常的
                            self.log_result("API端点", endpoint_name, True, f"端点可用 (状态码: {response.status_code})")
                        else:
                            self.log_result("API端点", endpoint_name, False, f"状态码: {response.status_code}")
                    
                    else:
                        # 普通GET请求
                        response = await client.get(f"{base_url}{endpoint_path}")
                        if response.status_code == 200:
                            self.log_result("API端点", endpoint_name, True, "端点正常")
                        else:
                            self.log_result("API端点", endpoint_name, False, f"状态码: {response.status_code}")
                
                except Exception as e:
                    self.log_result("API端点", endpoint_name, False, str(e))
    
    async def check_database_connections(self):
        """检查数据库连接"""
        base_url = SERVICES["主后端服务"]
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # 尝试访问需要数据库的端点
                response = await client.get(f"{base_url}/api/v1/users/")
                if response.status_code in [200, 401]:  # 401表示需要认证，但数据库连接正常
                    self.log_result("数据库", "PostgreSQL", True, "连接正常")
                else:
                    self.log_result("数据库", "PostgreSQL", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_result("数据库", "PostgreSQL", False, str(e))
    
    async def check_external_services(self):
        """检查外部服务"""
        # 检查Redis（通过集成测试端点）
        base_url = SERVICES["主后端服务"]
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                # 检查Dramatiq/Redis
                response = await client.post(f"{base_url}/api/v1/integration/dramatiq/hello?name=QuickTest")
                if response.status_code == 200:
                    self.log_result("外部服务", "Redis/Dramatiq", True, "任务队列正常")
                else:
                    self.log_result("外部服务", "Redis/Dramatiq", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_result("外部服务", "Redis/Dramatiq", False, str(e))
            
            try:
                # 检查Manticore
                response = await client.get(f"{base_url}/api/v1/integration/manticore/health")
                if response.status_code == 200:
                    self.log_result("外部服务", "Manticore Search", True, "搜索引擎正常")
                else:
                    self.log_result("外部服务", "Manticore Search", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_result("外部服务", "Manticore Search", False, str(e))
    
    async def run_quick_validation(self):
        """运行快速验证"""
        print("🚀 开始快速验证检查...")
        print("=" * 60)
        
        # 检查各个服务
        print("\n📡 检查服务状态...")
        for service_name, base_url in SERVICES.items():
            await self.check_service_health(service_name, base_url)
            await asyncio.sleep(0.5)
        
        # 检查API端点
        print("\n🔗 检查API端点...")
        await self.check_api_endpoints()
        
        # 检查数据库连接
        print("\n💾 检查数据库连接...")
        await self.check_database_connections()
        
        # 检查外部服务
        print("\n🔧 检查外部服务...")
        await self.check_external_services()
        
        # 输出总结
        self.print_summary()
    
    def print_summary(self):
        """打印验证总结"""
        print("\n" + "=" * 60)
        print("📊 快速验证总结")
        print("=" * 60)
        
        total_checks = len(self.results)
        passed_checks = sum(1 for r in self.results if r["success"])
        failed_checks = total_checks - passed_checks
        
        print(f"总检查项: {total_checks}")
        print(f"通过: {passed_checks} ✅")
        print(f"失败: {failed_checks} ❌")
        print(f"成功率: {(passed_checks/total_checks*100):.1f}%")
        
        # 按服务分组显示结果
        services = {}
        for result in self.results:
            service = result["service"]
            if service not in services:
                services[service] = {"passed": 0, "failed": 0, "details": []}
            
            if result["success"]:
                services[service]["passed"] += 1
            else:
                services[service]["failed"] += 1
                services[service]["details"].append(f"{result['endpoint']}: {result['details']}")
        
        print("\n📋 各服务状态:")
        for service, stats in services.items():
            total = stats["passed"] + stats["failed"]
            success_rate = (stats["passed"] / total * 100) if total > 0 else 0
            status = "✅" if stats["failed"] == 0 else "⚠️" if success_rate >= 50 else "❌"
            
            print(f"{status} {service}: {stats['passed']}/{total} 通过 ({success_rate:.0f}%)")
            
            if stats["details"]:
                for detail in stats["details"]:
                    print(f"    ❌ {detail}")
        
        # 给出建议
        print("\n💡 建议:")
        if failed_checks == 0:
            print("✅ 所有检查都通过了！系统状态良好。")
        elif failed_checks <= 2:
            print("⚠️ 有少量问题，但系统基本可用。建议检查失败的项目。")
        else:
            print("❌ 发现多个问题，建议检查服务配置和启动状态。")
            print("   1. 确认所有Docker容器都在运行: docker compose ps")
            print("   2. 检查服务日志: docker compose logs [service_name]")
            print("   3. 重启服务: docker compose restart")

async def main():
    """主函数"""
    validator = QuickValidator()
    await validator.run_quick_validation()

if __name__ == "__main__":
    asyncio.run(main())
