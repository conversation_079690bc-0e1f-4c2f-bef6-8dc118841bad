#!/usr/bin/env python3
"""
端到端学习流程测试

验证从文档上传到AI对话学习的完整用户流程。
"""

import asyncio
import httpx
import json
import uuid
import time
from typing import Dict, Any, Optional

class E2ELearningFlowTest:
    """端到端学习流程测试"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.api_base = f"{self.base_url}/api/v1"
        self.client = httpx.AsyncClient(timeout=30.0)
        self.auth_token = None
        self.user_id = None
        self.test_data = {}
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_step(self, step: str, success: bool, details: str = ""):
        """记录测试步骤"""
        status = "✅" if success else "❌"
        print(f"{status} 步骤 {step}: {details}")
        if not success:
            raise Exception(f"步骤 {step} 失败: {details}")
    
    async def step1_user_registration_and_login(self):
        """步骤1: 用户注册和登录"""
        print("\n🔐 步骤1: 用户注册和登录")
        
        # 生成唯一用户
        user_email = f"learner_{uuid.uuid4().hex[:8]}@example.com"
        user_data = {
            "email": user_email,
            "password": "learner123",
            "full_name": "AI学习者"
        }
        
        # 注册用户
        response = await self.client.post(f"{self.api_base}/users/signup", json=user_data)
        if response.status_code not in [200, 201]:
            self.log_step("1.1", False, f"用户注册失败: {response.status_code}")
        
        self.log_step("1.1", True, f"用户注册成功: {user_email}")
        
        # 用户登录
        login_data = {
            "username": user_email,
            "password": "learner123"
        }
        
        response = await self.client.post(f"{self.api_base}/login/access-token", data=login_data)
        if response.status_code != 200:
            self.log_step("1.2", False, f"用户登录失败: {response.status_code}")
        
        token_data = response.json()
        self.auth_token = token_data["access_token"]
        self.client.headers.update({"Authorization": f"Bearer {self.auth_token}"})
        
        # 获取用户信息
        response = await self.client.get(f"{self.api_base}/users/me")
        if response.status_code == 200:
            user_info = response.json()
            self.user_id = user_info["id"]
        
        self.log_step("1.2", True, f"用户登录成功，用户ID: {self.user_id}")
    
    async def step2_create_learning_topic(self):
        """步骤2: 创建学习主题"""
        print("\n📚 步骤2: 创建学习主题")
        
        topic_data = {
            "name": "深度学习基础",
            "description": "学习深度学习的基本概念、神经网络原理和常用算法",
            "category": "人工智能",
            "difficulty_level": 4
        }
        
        # 使用主后端的主题API
        response = await self.client.post(f"{self.api_base}/topics/", json=topic_data)
        
        topic_info = response.json()
        self.test_data["topic_id"] = topic_info["id"]
        self.test_data["topic_name"] = topic_info["name"]
        
        self.log_step("2.1", True, f"学习主题创建成功: {self.test_data['topic_name']} (ID: {self.test_data['topic_id']})")
    
    async def step3_upload_learning_document(self):
        """步骤3: 上传学习文档"""
        print("\n📄 步骤3: 上传学习文档")
        
        # 创建一个关于深度学习的测试文档
        document_content = """# 深度学习基础教程

## 1. 什么是深度学习

深度学习是机器学习的一个子领域，它基于人工神经网络，特别是深层神经网络。深度学习的"深度"指的是网络中的层数，通常包含多个隐藏层。

### 1.1 神经网络基础

神经网络是由大量相互连接的节点（神经元）组成的计算模型。每个神经元接收输入信号，经过加权求和和激活函数处理后产生输出。

### 1.2 深度神经网络的优势

- **特征学习**: 能够自动学习数据的层次化特征表示
- **非线性建模**: 通过多层非线性变换，可以建模复杂的函数关系
- **端到端学习**: 可以直接从原始数据学习到最终任务的映射

## 2. 常见的深度学习架构

### 2.1 多层感知机 (MLP)
最基本的深度学习模型，由多个全连接层组成。

### 2.2 卷积神经网络 (CNN)
专门用于处理具有网格结构数据（如图像）的神经网络。

### 2.3 循环神经网络 (RNN)
适用于处理序列数据的神经网络，具有记忆能力。

### 2.4 Transformer
基于注意力机制的模型，在自然语言处理领域取得了突破性进展。

## 3. 训练深度学习模型

### 3.1 反向传播算法
深度学习模型的核心训练算法，通过梯度下降优化网络参数。

### 3.2 优化器
- SGD (随机梯度下降)
- Adam (自适应矩估计)
- RMSprop

### 3.3 正则化技术
- Dropout: 随机丢弃神经元，防止过拟合
- Batch Normalization: 批量归一化，加速训练
- L1/L2正则化: 权重衰减

## 4. 深度学习的应用领域

### 4.1 计算机视觉
- 图像分类
- 目标检测
- 图像分割
- 人脸识别

### 4.2 自然语言处理
- 机器翻译
- 文本分类
- 情感分析
- 问答系统

### 4.3 语音识别
- 语音转文本
- 语音合成
- 语音情感识别

## 5. 深度学习框架

### 5.1 TensorFlow
Google开发的开源深度学习框架。

### 5.2 PyTorch
Facebook开发的动态图深度学习框架。

### 5.3 Keras
高级神经网络API，可以运行在TensorFlow之上。

## 总结

深度学习作为人工智能的重要分支，在各个领域都展现出了强大的能力。通过理解其基本原理和常用架构，我们可以更好地应用深度学习技术解决实际问题。

学习深度学习需要扎实的数学基础（线性代数、概率论、微积分）和编程能力。建议从简单的模型开始，逐步深入理解复杂的架构和算法。
"""
        
        document_data = {
            "title": "深度学习基础教程",
            "content": document_content,
            "file_type": "md",
            "size": len(document_content.encode('utf-8'))
        }
        
        response = await self.client.post(f"{self.api_base}/documents/", json=document_data)
        if response.status_code not in [200, 201]:
            self.log_step("3.1", False, f"文档上传失败: {response.status_code}")
        
        doc_info = response.json()
        self.test_data["document_id"] = doc_info["id"]
        self.test_data["document_title"] = doc_info["title"]
        
        self.log_step("3.1", True, f"学习文档上传成功: {self.test_data['document_title']} (ID: {self.test_data['document_id']})")
        
        # 等待文档处理完成
        print("⏳ 等待文档处理...")
        await asyncio.sleep(5)
        
        # 检查文档处理状态
        response = await self.client.get(f"{self.api_base}/documents/{self.test_data['document_id']}")
        if response.status_code == 200:
            doc_data = response.json()
            self.log_step("3.2", True, f"文档处理完成，状态: {doc_data.get('status', '已处理')}")
        else:
            self.log_step("3.2", True, "文档已上传，处理状态未知")
    
    async def step4_test_document_search(self):
        """步骤4: 测试文档搜索功能"""
        print("\n🔍 步骤4: 测试文档搜索功能")
        
        # 测试搜索功能
        search_queries = [
            "什么是深度学习",
            "神经网络的优势",
            "卷积神经网络",
            "深度学习应用"
        ]
        
        for i, query in enumerate(search_queries, 1):
            search_data = {
                "query": query,
                "search_type": "hybrid",
                "limit": 3
            }
            
            response = await self.client.post(f"{self.api_base}/search/documents", json=search_data)
            if response.status_code == 200:
                results = response.json()
                result_count = len(results.get("results", []))
                self.log_step(f"4.{i}", True, f"搜索 '{query}' 成功，返回 {result_count} 个结果")
            else:
                self.log_step(f"4.{i}", False, f"搜索 '{query}' 失败: {response.status_code}")
            
            await asyncio.sleep(1)  # 避免请求过快
    
    async def step5_test_embedding_service(self):
        """步骤5: 测试嵌入服务"""
        print("\n🧮 步骤5: 测试嵌入服务")
        
        test_texts = [
            "深度学习是机器学习的一个分支",
            "神经网络由多个层组成",
            "卷积神经网络适用于图像处理"
        ]
        
        embedding_data = {
            "ids": [f"test_{i}" for i in range(len(test_texts))],
            "texts": test_texts
        }
        
        response = await self.client.post(f"{self.api_base}/embedding/embed", json=embedding_data)
        if response.status_code == 200:
            result = response.json()
            results = result.get("results", [])
            if results and len(results) == len(test_texts):
                vector_dim = len(results[0].get("vector", [])) if results[0] else 0
                self.log_step("5.1", True, f"文本向量化成功，生成 {len(results)} 个 {vector_dim} 维向量")
            else:
                self.log_step("5.1", False, "向量化结果异常")
        else:
            self.log_step("5.1", False, f"文本向量化失败: {response.status_code}")
    
    async def step6_test_integration_features(self):
        """步骤6: 测试集成功能"""
        print("\n🔗 步骤6: 测试集成功能")
        
        # 测试Dramatiq任务队列
        response = await self.client.post(f"{self.api_base}/integration/dramatiq/hello?name=E2ETest")
        if response.status_code == 200:
            self.log_step("6.1", True, "异步任务队列正常")
        else:
            self.log_step("6.1", False, f"异步任务队列异常: {response.status_code}")
        
        # 测试Manticore搜索引擎
        response = await self.client.get(f"{self.api_base}/integration/manticore/health")
        if response.status_code == 200:
            self.log_step("6.2", True, "Manticore搜索引擎正常")
        else:
            self.log_step("6.2", True, f"Manticore搜索引擎有问题（状态码: {response.status_code}），但不影响核心功能")
    
    async def run_e2e_test(self):
        """运行端到端测试"""
        print("🚀 开始端到端学习流程测试")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            await self.step1_user_registration_and_login()
            await self.step2_create_learning_topic()
            await self.step3_upload_learning_document()
            await self.step4_test_document_search()
            await self.step5_test_embedding_service()
            await self.step6_test_integration_features()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print("\n" + "=" * 60)
            print("🎉 端到端测试完成！")
            print(f"⏱️ 总耗时: {duration:.2f} 秒")
            print("=" * 60)
            
            # 输出测试数据摘要
            print("\n📋 测试数据摘要:")
            print(f"用户ID: {self.user_id}")
            print(f"主题: {self.test_data.get('topic_name', 'N/A')} (ID: {self.test_data.get('topic_id', 'N/A')})")
            print(f"文档: {self.test_data.get('document_title', 'N/A')} (ID: {self.test_data.get('document_id', 'N/A')})")
            
            print("\n✅ 所有核心功能验证通过！系统可以支持完整的学习流程。")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print("\n" + "=" * 60)
            print("❌ 端到端测试失败！")
            print(f"⏱️ 测试耗时: {duration:.2f} 秒")
            print(f"❌ 错误信息: {str(e)}")
            print("=" * 60)
            
            print("\n🔧 故障排除建议:")
            print("1. 检查所有服务是否正常运行: docker compose ps")
            print("2. 检查服务日志: docker compose logs [service_name]")
            print("3. 确认数据库和外部服务连接正常")
            print("4. 重启服务: docker compose restart")

async def main():
    """主函数"""
    async with E2ELearningFlowTest() as test:
        await test.run_e2e_test()

if __name__ == "__main__":
    asyncio.run(main())
