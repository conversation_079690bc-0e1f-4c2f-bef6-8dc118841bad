#!/usr/bin/env python3
"""
WebSocket实时对话功能测试脚本

验证WebSocket连接、消息发送和接收功能
"""

import asyncio
import json
import uuid
import websockets
import httpx
from typing import Optional

# 配置
BASE_URL = "http://api.localhost.tiangolo.com"
WS_BASE_URL = "ws://api.localhost.tiangolo.com"
API_BASE = f"{BASE_URL}/api/v1"
TIMEOUT = 30.0

class WebSocketChatTester:
    """WebSocket对话测试器"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
        self.auth_token = None
        self.user_id = None
        self.conversation_id = None
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def setup_test_user(self) -> bool:
        """设置测试用户"""
        try:
            # 创建测试用户
            user_data = {
                "email": f"ws_test_{uuid.uuid4().hex[:8]}@example.com",
                "password": "testpassword123",
                "full_name": "WebSocket测试用户"
            }
            
            # 注册用户
            response = await self.client.post(
                f"{API_BASE}/users/signup",
                json=user_data
            )
            
            if response.status_code not in [200, 201]:
                print(f"❌ 用户注册失败: {response.status_code}")
                return False
            
            # 登录获取token
            login_data = {
                "username": user_data["email"],
                "password": user_data["password"]
            }
            
            response = await self.client.post(
                f"{API_BASE}/login/access-token",
                data=login_data
            )
            
            if response.status_code != 200:
                print(f"❌ 用户登录失败: {response.status_code}")
                return False
            
            token_data = response.json()
            self.auth_token = token_data["access_token"]
            self.client.headers.update({"Authorization": f"Bearer {self.auth_token}"})
            
            # 获取用户信息
            response = await self.client.get(f"{API_BASE}/users/me")
            if response.status_code == 200:
                user_info = response.json()
                self.user_id = user_info["id"]
            
            print(f"✅ 测试用户设置成功: {self.user_id}")
            return True
            
        except Exception as e:
            print(f"❌ 测试用户设置失败: {str(e)}")
            return False
    
    async def create_test_conversation(self) -> bool:
        """创建测试对话"""
        try:
            conversation_data = {
                "title": "WebSocket测试对话",
                "description": "用于测试WebSocket实时通信功能",
                "status": "active"
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/",
                json=conversation_data
            )
            
            if response.status_code not in [200, 201]:
                print(f"❌ 对话创建失败: {response.status_code}")
                return False
            
            conversation = response.json()
            self.conversation_id = conversation["id"]
            
            print(f"✅ 测试对话创建成功: {self.conversation_id}")
            return True
            
        except Exception as e:
            print(f"❌ 对话创建失败: {str(e)}")
            return False
    
    async def test_websocket_connection(self) -> bool:
        """测试WebSocket连接"""
        try:
            ws_url = f"{WS_BASE_URL}/api/v1/conversations/ws/{self.conversation_id}"
            print(f"🔗 连接WebSocket: {ws_url}")
            
            async with websockets.connect(ws_url) as websocket:
                print("✅ WebSocket连接建立成功")
                
                # 接收连接确认消息
                response = await websocket.recv()
                data = json.loads(response)
                print(f"📨 收到连接确认: {data}")
                
                if data.get("type") != "connection_established":
                    print("❌ 连接确认消息格式错误")
                    return False
                
                # 发送测试消息
                test_message = {
                    "message": "Hello WebSocket! 这是一条测试消息。",
                    "timestamp": "2025-08-15T06:00:00Z"
                }
                
                print(f"📤 发送测试消息: {test_message['message']}")
                await websocket.send(json.dumps(test_message))
                
                # 接收处理中状态
                response = await websocket.recv()
                processing_data = json.loads(response)
                print(f"📨 收到处理状态: {processing_data}")
                
                # 接收AI回复
                response = await websocket.recv()
                reply_data = json.loads(response)
                print(f"📨 收到AI回复: {reply_data}")
                
                if reply_data.get("type") != "ai_response":
                    print("❌ AI回复消息格式错误")
                    return False
                
                # 发送第二条消息测试连续对话
                test_message2 = {
                    "message": "这是第二条测试消息，用于验证连续对话功能。",
                    "timestamp": "2025-08-15T06:01:00Z"
                }
                
                print(f"📤 发送第二条消息: {test_message2['message']}")
                await websocket.send(json.dumps(test_message2))
                
                # 接收第二次回复
                await websocket.recv()  # 处理状态
                response = await websocket.recv()  # AI回复
                reply_data2 = json.loads(response)
                print(f"📨 收到第二次AI回复: {reply_data2}")
                
                print("✅ WebSocket双向通信测试成功")
                return True
                
        except Exception as e:
            print(f"❌ WebSocket连接测试失败: {str(e)}")
            return False
    
    async def test_websocket_error_handling(self) -> bool:
        """测试WebSocket错误处理"""
        try:
            ws_url = f"{WS_BASE_URL}/api/v1/conversations/ws/{self.conversation_id}"
            
            async with websockets.connect(ws_url) as websocket:
                # 接收连接确认
                await websocket.recv()
                
                # 发送格式错误的消息
                invalid_message = {"invalid": "message without required fields"}
                
                print(f"📤 发送无效消息: {invalid_message}")
                await websocket.send(json.dumps(invalid_message))
                
                # 接收错误响应
                response = await websocket.recv()
                error_data = json.loads(response)
                print(f"📨 收到错误响应: {error_data}")
                
                if error_data.get("type") != "error":
                    print("❌ 错误处理测试失败")
                    return False
                
                print("✅ WebSocket错误处理测试成功")
                return True
                
        except Exception as e:
            print(f"❌ WebSocket错误处理测试失败: {str(e)}")
            return False
    
    async def run_websocket_tests(self):
        """运行完整的WebSocket测试"""
        print("🚀 开始WebSocket实时对话功能测试")
        print("=" * 60)
        
        try:
            # 设置测试环境
            if not await self.setup_test_user():
                return False
            
            if not await self.create_test_conversation():
                return False
            
            # 运行WebSocket测试
            if not await self.test_websocket_connection():
                return False
            
            if not await self.test_websocket_error_handling():
                return False
            
            print("\n" + "=" * 60)
            print("🎉 WebSocket实时对话功能测试完成！")
            print("✅ 所有WebSocket功能验证通过！")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print("\n" + "=" * 60)
            print("❌ WebSocket功能测试失败！")
            print(f"❌ 错误信息: {str(e)}")
            print("=" * 60)
            return False

async def main():
    """主函数"""
    async with WebSocketChatTester() as tester:
        success = await tester.run_websocket_tests()
        return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        exit(1)
