#!/usr/bin/env python3
"""
用户故事验证脚本

基于用户故事验证知深学习导师项目的各个功能模块实现。
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, Any
import httpx
import os
from pathlib import Path

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"
TIMEOUT = 30.0

class UserStoryValidator:
    """用户故事验证器"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
        self.auth_token = None
        self.test_user_id = None
        self.test_results = []
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_result(self, story_id: str, description: str, success: bool, details: str = ""):
        """记录测试结果"""
        result = {
            "story_id": story_id,
            "description": description,
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {story_id}: {description}")
        if details:
            print(f"   详情: {details}")
    
    async def setup_test_user(self) -> bool:
        """设置测试用户"""
        try:
            # 创建测试用户
            user_data = {
                "email": f"test_{uuid.uuid4().hex[:8]}@example.com",
                "password": "testpassword123",
                "full_name": "Test User"
            }
            
            # 注册用户
            response = await self.client.post(
                f"{API_BASE}/users/signup",
                json=user_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_result("SETUP", "用户注册", False, f"状态码: {response.status_code}")
                return False
            
            # 登录获取token
            login_data = {
                "username": user_data["email"],
                "password": user_data["password"]
            }
            
            response = await self.client.post(
                f"{API_BASE}/login/access-token",
                data=login_data
            )
            
            if response.status_code != 200:
                self.log_result("SETUP", "用户登录", False, f"状态码: {response.status_code}")
                return False
            
            token_data = response.json()
            self.auth_token = token_data["access_token"]
            self.client.headers.update({"Authorization": f"Bearer {self.auth_token}"})
            
            # 获取用户信息
            response = await self.client.get(f"{API_BASE}/users/me")
            if response.status_code == 200:
                user_info = response.json()
                self.test_user_id = user_info["id"]
            
            self.log_result("SETUP", "测试用户设置", True, f"用户ID: {self.test_user_id}")
            return True
            
        except Exception as e:
            self.log_result("SETUP", "测试用户设置", False, str(e))
            return False
    
    async def validate_us001_user_auth(self) -> bool:
        """US-001: 用户注册与登录"""
        try:
            # 测试用户信息获取
            response = await self.client.get(f"{API_BASE}/users/me")
            
            if response.status_code != 200:
                self.log_result("US-001", "用户认证验证", False, f"无法获取用户信息: {response.status_code}")
                return False
            
            user_data = response.json()
            required_fields = ["id", "email", "full_name", "is_active"]
            
            for field in required_fields:
                if field not in user_data:
                    self.log_result("US-001", "用户认证验证", False, f"缺少字段: {field}")
                    return False
            
            self.log_result("US-001", "用户注册与登录", True, "JWT认证和用户信息获取正常")
            return True
            
        except Exception as e:
            self.log_result("US-001", "用户注册与登录", False, str(e))
            return False
    
    async def validate_us002_document_upload(self) -> bool:
        """US-002: 文档上传与处理"""
        try:
            # 创建测试文档
            test_content = """# 测试文档
            
这是一个用于测试的Markdown文档。

## 第一章：基础概念
人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。

## 第二章：机器学习
机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。

## 第三章：深度学习
深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。
"""
            
            document_data = {
                "title": "AI学习测试文档",
                "content": test_content,
                "file_type": "md",
                "size": len(test_content.encode('utf-8'))
            }
            
            # 上传文档
            response = await self.client.post(
                f"{API_BASE}/documents/",
                json=document_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_result("US-002", "文档上传", False, f"上传失败: {response.status_code}")
                return False
            
            doc_data = response.json()
            doc_id = doc_data["id"]
            
            # 验证文档信息
            response = await self.client.get(f"{API_BASE}/documents/{doc_id}")
            if response.status_code != 200:
                self.log_result("US-002", "文档上传", False, "无法获取上传的文档")
                return False
            
            # 等待异步处理
            await asyncio.sleep(2)
            
            # 检查文档处理状态
            response = await self.client.get(f"{API_BASE}/documents/{doc_id}/stats")
            if response.status_code == 200:
                stats = response.json()
                self.log_result("US-002", "文档上传与处理", True, 
                              f"文档ID: {doc_id}, 处理状态: {stats}")
            else:
                self.log_result("US-002", "文档上传与处理", True, 
                              f"文档上传成功，ID: {doc_id}")
            
            return True
            
        except Exception as e:
            self.log_result("US-002", "文档上传与处理", False, str(e))
            return False
    
    async def validate_us003_topic_management(self) -> bool:
        """US-003: 学习主题创建"""
        try:
            # 创建主题
            topic_data = {
                "name": "人工智能基础",
                "description": "学习人工智能的基础概念和原理",
                "category": "计算机科学",
                "difficulty_level": 3
            }
            
            # 使用主后端的主题API
            response = await self.client.post(
                f"{API_BASE}/topics/",
                json=topic_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_result("US-003", "主题创建", False, f"创建失败: {response.status_code}")
                return False

            topic_info = response.json()
            topic_id = topic_info["id"]

            # 获取主题列表
            response = await self.client.get(f"{API_BASE}/topics/")
            
            if response.status_code != 200:
                self.log_result("US-003", "主题管理", False, "无法获取主题列表")
                return False
            
            topics = response.json()
            self.log_result("US-003", "学习主题创建", True, 
                          f"主题ID: {topic_id}, 总数: {topics.get('count', 'N/A')}")
            return True
            
        except Exception as e:
            self.log_result("US-003", "学习主题创建", False, str(e))
            return False
    
    async def validate_us004_text_splitting(self) -> bool:
        """US-004: 文档智能分割"""
        try:
            # 检查文本分割引擎
            test_text = """这是一个测试文档。它包含多个段落。

第一段讲述了基本概念。这里有一些详细的解释。

第二段介绍了具体的实现方法。包括技术细节和最佳实践。

第三段总结了主要要点。"""
            
            # 尝试通过文档处理API测试分割
            document_data = {
                "title": "分割测试文档",
                "content": test_text,
                "file_type": "txt",
                "size": len(test_text.encode('utf-8'))
            }
            
            response = await self.client.post(
                f"{API_BASE}/documents/",
                json=document_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_result("US-004", "文档智能分割", False, "无法创建测试文档")
                return False
            
            doc_data = response.json()
            doc_id = doc_data["id"]
            
            # 等待处理完成
            await asyncio.sleep(3)
            
            # 检查文档块
            response = await self.client.get(f"{API_BASE}/documents/{doc_id}/chunks")
            if response.status_code == 200:
                chunks = response.json()
                chunk_count = len(chunks) if isinstance(chunks, list) else chunks.get('count', 0)
                self.log_result("US-004", "文档智能分割", True, 
                              f"文档分割成功，生成 {chunk_count} 个文本块")
            else:
                self.log_result("US-004", "文档智能分割", True, 
                              "文档创建成功，分割功能可能在后台处理")
            
            return True
            
        except Exception as e:
            self.log_result("US-004", "文档智能分割", False, str(e))
            return False
    
    async def validate_us005_embedding(self) -> bool:
        """US-005: 文本向量化处理"""
        try:
            # 测试嵌入API
            test_texts = [
                "人工智能是计算机科学的一个分支",
                "机器学习是AI的重要组成部分",
                "深度学习使用神经网络"
            ]
            
            embedding_data = {
                "ids": [f"test_{i}" for i in range(len(test_texts))],
                "texts": test_texts
            }
            
            response = await self.client.post(
                f"{API_BASE}/embedding/embed",
                json=embedding_data
            )
            
            if response.status_code != 200:
                self.log_result("US-005", "文本向量化", False, f"向量化失败: {response.status_code}")
                return False
            
            result = response.json()
            results = result.get("results", [])

            if len(results) != len(test_texts):
                self.log_result("US-005", "文本向量化", False, "向量数量不匹配")
                return False

            # 检查向量维度
            if results and len(results[0].get("vector", [])) > 0:
                vector_dim = len(results[0]["vector"])
                self.log_result("US-005", "文本向量化处理", True,
                              f"成功生成 {len(results)} 个向量，维度: {vector_dim}")
            else:
                self.log_result("US-005", "文本向量化处理", False, "向量为空")
                return False
            
            return True
            
        except Exception as e:
            self.log_result("US-005", "文本向量化处理", False, str(e))
            return False
    
    async def validate_us006_search(self) -> bool:
        """US-006: 智能上下文检索"""
        try:
            # 测试搜索功能
            search_data = {
                "query": "人工智能",
                "search_type": "hybrid",
                "limit": 5
            }
            
            response = await self.client.post(
                f"{API_BASE}/search/documents",
                json=search_data
            )
            
            if response.status_code != 200:
                self.log_result("US-006", "智能搜索", False, f"搜索失败: {response.status_code}")
                return False
            
            results = response.json()
            
            # 检查搜索结果结构
            if "results" in results:
                result_count = len(results["results"])
                self.log_result("US-006", "智能上下文检索", True, 
                              f"搜索成功，返回 {result_count} 个结果")
            else:
                self.log_result("US-006", "智能上下文检索", True, "搜索API响应正常")
            
            return True
            
        except Exception as e:
            self.log_result("US-006", "智能上下文检索", False, str(e))
            return False
    
    async def validate_system_health(self) -> bool:
        """验证系统健康状态"""
        try:
            # 检查基础健康状态
            response = await self.client.get(f"{API_BASE}/utils/health-check/")

            if response.status_code not in [200, 307]:  # 307是重定向，也算正常
                self.log_result("HEALTH", "系统健康检查", False, f"健康检查失败: {response.status_code}")
                return False

            if response.status_code == 200:
                health_data = response.json()
                self.log_result("HEALTH", "系统健康检查", True, f"状态: {health_data}")
            else:
                self.log_result("HEALTH", "系统健康检查", True, "健康检查正常（重定向）")
            return True
            
        except Exception as e:
            self.log_result("HEALTH", "系统健康检查", False, str(e))
            return False
    
    async def run_all_validations(self):
        """运行所有验证测试"""
        print("🚀 开始用户故事验证测试...")
        print("=" * 60)
        
        # 系统健康检查
        await self.validate_system_health()
        
        # 设置测试用户
        if not await self.setup_test_user():
            print("❌ 测试用户设置失败，终止测试")
            return
        
        # 运行各个用户故事验证
        validations = [
            self.validate_us001_user_auth,
            self.validate_us002_document_upload,
            self.validate_us003_topic_management,
            self.validate_us004_text_splitting,
            self.validate_us005_embedding,
            self.validate_us006_search,
        ]
        
        for validation in validations:
            try:
                await validation()
                await asyncio.sleep(1)  # 避免请求过快
            except Exception as e:
                print(f"❌ 验证过程出错: {e}")
        
        # 输出总结
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 验证测试总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['story_id']}: {result['description']}")
                    if result["details"]:
                        print(f"    详情: {result['details']}")
        
        # 保存结果到文件
        results_file = Path("test_results.json")
        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细结果已保存到: {results_file}")

async def main():
    """主函数"""
    async with UserStoryValidator() as validator:
        await validator.run_all_validations()

if __name__ == "__main__":
    asyncio.run(main())
