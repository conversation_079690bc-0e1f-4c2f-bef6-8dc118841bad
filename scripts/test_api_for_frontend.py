#!/usr/bin/env python3
"""
前端API测试脚本

为前端开发者提供API功能验证，确保所有端点正常工作
"""

import asyncio
import json
import uuid
from typing import Dict, Any
import httpx

# 配置
BASE_URL = "http://api.localhost.tiangolo.com"
API_BASE = f"{BASE_URL}/api/v1"
TIMEOUT = 30.0

class FrontendApiTester:
    """前端API测试器"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
        self.auth_token = None
        self.test_data = {}
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_test(self, test_name: str, success: bool, data: Any = None):
        """记录测试结果"""
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
        if data and success:
            print(f"   数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
        print()
    
    async def test_user_registration(self) -> bool:
        """测试用户注册"""
        try:
            user_data = {
                "email": f"frontend_test_{uuid.uuid4().hex[:8]}@example.com",
                "password": "testpassword123",
                "full_name": "前端测试用户"
            }
            
            response = await self.client.post(
                f"{API_BASE}/users/signup",
                json=user_data
            )
            
            if response.status_code in [200, 201]:
                user_info = response.json()
                self.test_data['user'] = user_info
                self.test_data['credentials'] = {
                    "email": user_data["email"],
                    "password": user_data["password"]
                }
                self.log_test("用户注册", True, user_info)
                return True
            else:
                self.log_test("用户注册", False)
                return False
                
        except Exception as e:
            print(f"❌ 用户注册失败: {str(e)}")
            return False
    
    async def test_user_login(self) -> bool:
        """测试用户登录"""
        try:
            login_data = {
                "username": self.test_data['credentials']['email'],
                "password": self.test_data['credentials']['password']
            }
            
            response = await self.client.post(
                f"{API_BASE}/login/access-token",
                data=login_data
            )
            
            if response.status_code == 200:
                token_data = response.json()
                self.auth_token = token_data["access_token"]
                self.client.headers.update({"Authorization": f"Bearer {self.auth_token}"})
                self.test_data['token'] = token_data
                self.log_test("用户登录", True, {"token_type": token_data["token_type"]})
                return True
            else:
                self.log_test("用户登录", False)
                return False
                
        except Exception as e:
            print(f"❌ 用户登录失败: {str(e)}")
            return False
    
    async def test_get_current_user(self) -> bool:
        """测试获取当前用户信息"""
        try:
            response = await self.client.get(f"{API_BASE}/users/me")
            
            if response.status_code == 200:
                user_info = response.json()
                self.log_test("获取用户信息", True, user_info)
                return True
            else:
                self.log_test("获取用户信息", False)
                return False
                
        except Exception as e:
            print(f"❌ 获取用户信息失败: {str(e)}")
            return False
    
    async def test_topic_management(self) -> bool:
        """测试主题管理"""
        try:
            # 创建主题
            topic_data = {
                "name": "前端测试主题",
                "description": "用于前端API测试的主题",
                "category": "测试",
                "difficulty_level": 2
            }
            
            response = await self.client.post(
                f"{API_BASE}/topics/",
                json=topic_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_test("创建主题", False)
                return False
            
            topic = response.json()
            self.test_data['topic'] = topic
            self.log_test("创建主题", True, topic)
            
            # 获取主题列表
            response = await self.client.get(f"{API_BASE}/topics/?skip=0&limit=10")
            
            if response.status_code == 200:
                topics_data = response.json()
                self.log_test("获取主题列表", True, {"count": topics_data["count"]})
                return True
            else:
                self.log_test("获取主题列表", False)
                return False
                
        except Exception as e:
            print(f"❌ 主题管理测试失败: {str(e)}")
            return False
    
    async def test_document_management(self) -> bool:
        """测试文档管理"""
        try:
            # 上传文档
            document_content = """
# 前端API测试文档

这是一个用于测试前端API集成的示例文档。

## 内容概述

- API端点测试
- 数据格式验证
- 错误处理测试

## 测试要点

1. 用户认证流程
2. 数据CRUD操作
3. 实时通信功能
"""
            
            document_data = {
                "title": "前端API测试文档",
                "content": document_content,
                "file_type": "md",
                "size": len(document_content.encode('utf-8'))
            }
            
            response = await self.client.post(
                f"{API_BASE}/documents/",
                json=document_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_test("上传文档", False)
                return False
            
            document = response.json()
            self.test_data['document'] = document
            self.log_test("上传文档", True, {"id": document["id"], "title": document["title"]})
            
            # 获取文档列表
            response = await self.client.get(f"{API_BASE}/documents/?skip=0&limit=10")
            
            if response.status_code == 200:
                documents_data = response.json()
                self.log_test("获取文档列表", True, {"count": documents_data["count"]})
                return True
            else:
                self.log_test("获取文档列表", False)
                return False
                
        except Exception as e:
            print(f"❌ 文档管理测试失败: {str(e)}")
            return False
    
    async def test_conversation_management(self) -> bool:
        """测试对话管理"""
        try:
            # 创建对话
            conversation_data = {
                "title": "前端API测试对话",
                "description": "用于测试前端API的对话",
                "topic_id": self.test_data['topic']['id']
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/",
                json=conversation_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_test("创建对话", False)
                return False
            
            conversation = response.json()
            self.test_data['conversation'] = conversation
            self.log_test("创建对话", True, {"id": conversation["id"], "title": conversation["title"]})
            
            # 发送消息
            chat_request = {
                "message": "请介绍一下这个测试文档的内容",
                "conversation_id": conversation["id"],
                "use_context": True,
                "temperature": 0.7
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/chat",
                json=chat_request
            )
            
            if response.status_code == 200:
                chat_response = response.json()
                self.log_test("发送消息", True, {"message_length": len(chat_response["message"])})
                
                # 获取对话历史
                response = await self.client.get(
                    f"{API_BASE}/conversations/{conversation['id']}/messages?skip=0&limit=10"
                )
                
                if response.status_code == 200:
                    messages_data = response.json()
                    self.log_test("获取对话历史", True, {"count": messages_data["count"]})
                    return True
                else:
                    self.log_test("获取对话历史", False)
                    return False
            else:
                self.log_test("发送消息", False)
                return False
                
        except Exception as e:
            print(f"❌ 对话管理测试失败: {str(e)}")
            return False
    
    async def test_conversation_summary(self) -> bool:
        """测试对话摘要"""
        try:
            conversation_id = self.test_data['conversation']['id']
            
            response = await self.client.post(
                f"{API_BASE}/conversations/{conversation_id}/summary",
                json={"force_regenerate": True}
            )
            
            if response.status_code == 200:
                summary_data = response.json()
                self.log_test("生成对话摘要", True, {"summary_keys": list(summary_data["summary"].keys())})
                return True
            else:
                self.log_test("生成对话摘要", False)
                return False
                
        except Exception as e:
            print(f"❌ 对话摘要测试失败: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有API测试"""
        print("🚀 开始前端API功能测试")
        print("=" * 60)
        
        tests = [
            ("用户注册", self.test_user_registration),
            ("用户登录", self.test_user_login),
            ("获取用户信息", self.test_get_current_user),
            ("主题管理", self.test_topic_management),
            ("文档管理", self.test_document_management),
            ("对话管理", self.test_conversation_management),
            ("对话摘要", self.test_conversation_summary),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"🧪 测试: {test_name}")
            try:
                success = await test_func()
                if success:
                    passed += 1
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {str(e)}")
            print("-" * 40)
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有API测试通过！前端可以开始集成开发。")
        else:
            print("⚠️ 部分测试失败，请检查API服务状态。")
        
        print("\n📋 测试数据摘要:")
        for key, value in self.test_data.items():
            if isinstance(value, dict) and 'id' in value:
                print(f"  {key}: {value['id']}")
            elif key == 'credentials':
                print(f"  {key}: {value['email']}")
        
        print("=" * 60)
        return passed == total

async def main():
    """主函数"""
    async with FrontendApiTester() as tester:
        success = await tester.run_all_tests()
        return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
