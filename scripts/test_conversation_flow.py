#!/usr/bin/env python3
"""
智能对话功能测试脚本

验证核心对话体验：
1. 智能对话交互
2. 动态摘要生成  
3. 智能上下文管理
"""

import asyncio
import json
import time
import uuid
from typing import Dict, List, Optional, Any
import httpx

# 配置
BASE_URL = "http://api.localhost.tiangolo.com"
API_BASE = f"{BASE_URL}/api/v1"
TIMEOUT = 30.0

class ConversationFlowTester:
    """对话流程测试器"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
        self.auth_token = None
        self.user_id = None
        self.test_results = []
        self.test_data = {}
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    def log_step(self, step: str, success: bool, details: str = ""):
        """记录测试步骤"""
        result = {
            "step": step,
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {step}: {details}")
        if not success:
            raise Exception(f"步骤 {step} 失败: {details}")
    
    async def setup_test_user(self) -> bool:
        """设置测试用户"""
        try:
            # 创建测试用户
            user_data = {
                "email": f"conversation_test_{uuid.uuid4().hex[:8]}@example.com",
                "password": "testpassword123",
                "full_name": "对话测试用户"
            }
            
            # 注册用户
            response = await self.client.post(
                f"{API_BASE}/users/signup",
                json=user_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_step("用户注册", False, f"状态码: {response.status_code}")
                return False
            
            # 登录获取token
            login_data = {
                "username": user_data["email"],
                "password": user_data["password"]
            }
            
            response = await self.client.post(
                f"{API_BASE}/login/access-token",
                data=login_data
            )
            
            if response.status_code != 200:
                self.log_step("用户登录", False, f"状态码: {response.status_code}")
                return False
            
            token_data = response.json()
            self.auth_token = token_data["access_token"]
            self.client.headers.update({"Authorization": f"Bearer {self.auth_token}"})
            
            # 获取用户信息
            response = await self.client.get(f"{API_BASE}/users/me")
            if response.status_code == 200:
                user_info = response.json()
                self.user_id = user_info["id"]
            
            self.log_step("测试用户设置", True, f"用户ID: {self.user_id}")
            return True
            
        except Exception as e:
            self.log_step("测试用户设置", False, str(e))
            return False
    
    async def test_conversation_creation(self):
        """测试对话创建"""
        try:
            conversation_data = {
                "title": "深度学习讨论",
                "description": "关于深度学习基础概念的讨论",
                "status": "active"
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/",
                json=conversation_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_step("对话创建", False, f"状态码: {response.status_code}, 响应: {response.text}")
                return False
            
            conversation = response.json()
            self.test_data["conversation_id"] = conversation["id"]
            self.test_data["conversation_title"] = conversation["title"]
            
            self.log_step("对话创建", True, f"对话ID: {conversation['id']}")
            return True
            
        except Exception as e:
            self.log_step("对话创建", False, str(e))
            return False
    
    async def test_smart_chat_interaction(self):
        """测试智能对话交互"""
        try:
            # 第一轮对话
            chat_request = {
                "message": "什么是深度学习？请简单介绍一下基本概念。",
                "conversation_id": self.test_data["conversation_id"],
                "use_context": True,
                "temperature": 0.7
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/chat",
                json=chat_request
            )
            
            if response.status_code != 200:
                self.log_step("智能对话-第1轮", False, f"状态码: {response.status_code}, 响应: {response.text}")
                return False
            
            chat_response = response.json()
            self.test_data["first_response"] = chat_response["message"]
            
            self.log_step("智能对话-第1轮", True, f"AI回复长度: {len(chat_response['message'])} 字符")
            
            # 等待一秒
            await asyncio.sleep(1)
            
            # 第二轮对话 - 测试上下文理解
            chat_request2 = {
                "message": "那神经网络是如何工作的呢？",
                "conversation_id": self.test_data["conversation_id"],
                "use_context": True,
                "temperature": 0.7
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/chat",
                json=chat_request2
            )
            
            if response.status_code != 200:
                self.log_step("智能对话-第2轮", False, f"状态码: {response.status_code}")
                return False
            
            chat_response2 = response.json()
            self.test_data["second_response"] = chat_response2["message"]
            
            self.log_step("智能对话-第2轮", True, f"AI回复长度: {len(chat_response2['message'])} 字符")
            
            # 第三轮对话 - 测试深入讨论
            chat_request3 = {
                "message": "能举个具体的应用例子吗？比如在图像识别方面。",
                "conversation_id": self.test_data["conversation_id"],
                "use_context": True,
                "temperature": 0.7
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/chat",
                json=chat_request3
            )
            
            if response.status_code != 200:
                self.log_step("智能对话-第3轮", False, f"状态码: {response.status_code}")
                return False
            
            chat_response3 = response.json()
            self.test_data["third_response"] = chat_response3["message"]
            
            self.log_step("智能对话-第3轮", True, f"AI回复长度: {len(chat_response3['message'])} 字符")
            
            return True
            
        except Exception as e:
            self.log_step("智能对话交互", False, str(e))
            return False
    
    async def test_conversation_history(self):
        """测试对话历史管理"""
        try:
            # 获取对话消息历史
            response = await self.client.get(
                f"{API_BASE}/conversations/{self.test_data['conversation_id']}/messages"
            )
            
            if response.status_code != 200:
                self.log_step("对话历史获取", False, f"状态码: {response.status_code}")
                return False
            
            messages_data = response.json()
            messages = messages_data.get("data", [])
            
            # 验证消息数量（应该有6条：3个用户消息 + 3个AI回复）
            expected_count = 6
            actual_count = len(messages)
            
            if actual_count != expected_count:
                self.log_step("对话历史验证", False, f"消息数量不匹配，期望: {expected_count}, 实际: {actual_count}")
                return False
            
            # 验证消息顺序和角色
            roles = [msg["role"] for msg in messages]
            expected_roles = ["user", "assistant", "user", "assistant", "user", "assistant"]
            
            if roles != expected_roles:
                self.log_step("对话历史验证", False, f"消息角色顺序不正确: {roles}")
                return False
            
            self.log_step("对话历史管理", True, f"成功获取 {actual_count} 条消息，角色顺序正确")
            return True
            
        except Exception as e:
            self.log_step("对话历史管理", False, str(e))
            return False
    
    async def test_conversation_summary(self):
        """测试对话摘要生成"""
        try:
            # 手动触发摘要生成
            summary_request = {
                "conversation_id": self.test_data["conversation_id"],
                "force_regenerate": True
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/{self.test_data['conversation_id']}/summary",
                json=summary_request
            )
            
            if response.status_code != 200:
                self.log_step("摘要生成", False, f"状态码: {response.status_code}, 响应: {response.text}")
                return False
            
            summary_data = response.json()
            summary = summary_data.get("summary", {})
            
            # 验证摘要结构
            required_keys = ["user_summary", "assistant_summary"]
            for key in required_keys:
                if key not in summary:
                    self.log_step("摘要结构验证", False, f"缺少字段: {key}")
                    return False
            
            self.test_data["summary"] = summary
            self.log_step("对话摘要生成", True, f"摘要生成成功，包含用户摘要和AI摘要")
            return True
            
        except Exception as e:
            self.log_step("对话摘要生成", False, str(e))
            return False
    
    async def test_context_intelligence(self):
        """测试智能上下文管理"""
        try:
            # 创建一个新对话，测试上下文检索
            new_conversation_data = {
                "title": "上下文测试对话",
                "description": "测试智能上下文检索功能"
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/",
                json=new_conversation_data
            )
            
            if response.status_code not in [200, 201]:
                self.log_step("上下文测试对话创建", False, f"状态码: {response.status_code}")
                return False
            
            new_conversation = response.json()
            new_conversation_id = new_conversation["id"]
            
            # 发送一个需要上下文的问题
            context_chat_request = {
                "message": "基于之前的讨论，深度学习在实际应用中有哪些挑战？",
                "conversation_id": new_conversation_id,
                "use_context": True,
                "temperature": 0.7
            }
            
            response = await self.client.post(
                f"{API_BASE}/conversations/chat",
                json=context_chat_request
            )
            
            if response.status_code != 200:
                self.log_step("上下文智能检索", False, f"状态码: {response.status_code}")
                return False
            
            context_response = response.json()
            context_used = context_response.get("context_used", [])
            
            self.log_step("智能上下文管理", True, f"上下文检索成功，使用了 {len(context_used)} 个上下文片段")
            return True
            
        except Exception as e:
            self.log_step("智能上下文管理", False, str(e))
            return False
    
    async def test_conversation_status_management(self):
        """测试对话状态管理"""
        try:
            # 更新对话状态为已完成
            response = await self.client.put(
                f"{API_BASE}/conversations/{self.test_data['conversation_id']}/status?status=completed"
            )
            
            if response.status_code != 200:
                self.log_step("对话状态更新", False, f"状态码: {response.status_code}")
                return False
            
            # 验证状态更新
            response = await self.client.get(
                f"{API_BASE}/conversations/{self.test_data['conversation_id']}"
            )
            
            if response.status_code != 200:
                self.log_step("对话状态验证", False, f"状态码: {response.status_code}")
                return False
            
            conversation = response.json()
            if conversation["status"] != "completed":
                self.log_step("对话状态验证", False, f"状态未正确更新: {conversation['status']}")
                return False
            
            self.log_step("对话状态管理", True, "状态更新为已完成")
            return True
            
        except Exception as e:
            self.log_step("对话状态管理", False, str(e))
            return False
    
    async def run_conversation_flow_test(self):
        """运行完整的对话流程测试"""
        print("🚀 开始智能对话功能测试")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 设置测试用户
            await self.setup_test_user()
            
            # 运行各项测试
            await self.test_conversation_creation()
            await self.test_smart_chat_interaction()
            await self.test_conversation_history()
            await self.test_conversation_summary()
            await self.test_context_intelligence()
            await self.test_conversation_status_management()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print("\n" + "=" * 60)
            print("🎉 智能对话功能测试完成！")
            print(f"⏱️ 总耗时: {duration:.2f} 秒")
            print("=" * 60)
            
            # 输出测试数据摘要
            print("\n📋 测试数据摘要:")
            print(f"用户ID: {self.user_id}")
            print(f"对话: {self.test_data.get('conversation_title', 'N/A')} (ID: {self.test_data.get('conversation_id', 'N/A')})")
            print(f"AI回复示例: {self.test_data.get('first_response', 'N/A')[:100]}...")
            
            print("\n✅ 所有对话功能验证通过！智能对话系统可以正常工作。")
            
            # 保存测试结果
            with open("conversation_test_results.json", "w", encoding="utf-8") as f:
                json.dump({
                    "test_results": self.test_results,
                    "test_data": self.test_data,
                    "duration": duration,
                    "success": True
                }, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            print("\n" + "=" * 60)
            print("❌ 智能对话功能测试失败！")
            print(f"⏱️ 测试耗时: {duration:.2f} 秒")
            print(f"❌ 错误信息: {str(e)}")
            print("=" * 60)
            
            print("\n🔧 故障排除建议:")
            print("1. 检查对话API路由是否正确加载")
            print("2. 检查LLM服务配置和API密钥")
            print("3. 检查数据库连接和对话表结构")
            print("4. 查看后端服务日志: docker compose logs backend")
            
            # 保存失败结果
            with open("conversation_test_results.json", "w", encoding="utf-8") as f:
                json.dump({
                    "test_results": self.test_results,
                    "test_data": self.test_data,
                    "duration": duration,
                    "success": False,
                    "error": str(e)
                }, f, ensure_ascii=False, indent=2)
            
            return False

async def main():
    """主函数"""
    async with ConversationFlowTester() as tester:
        success = await tester.run_conversation_flow_test()
        return success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
