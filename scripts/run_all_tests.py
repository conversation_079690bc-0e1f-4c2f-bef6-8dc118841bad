#!/usr/bin/env python3
"""
测试运行器

运行所有的验证测试脚本，提供统一的测试入口。
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path
import argparse

class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.results = []
    
    def log_result(self, test_name: str, success: bool, duration: float, details: str = ""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "duration": duration,
            "details": details
        }
        self.results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name} - 耗时: {duration:.2f}s")
        if details:
            print(f"   详情: {details}")
    
    async def run_script(self, script_name: str, description: str) -> bool:
        """运行测试脚本"""
        script_path = self.script_dir / script_name
        
        if not script_path.exists():
            self.log_result(description, False, 0, f"脚本不存在: {script_path}")
            return False
        
        print(f"\n🚀 运行 {description}...")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            # 运行Python脚本
            process = await asyncio.create_subprocess_exec(
                sys.executable, str(script_path),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            end_time = time.time()
            duration = end_time - start_time
            
            if process.returncode == 0:
                self.log_result(description, True, duration, "执行成功")
                print(stdout.decode('utf-8'))
                return True
            else:
                self.log_result(description, False, duration, f"退出码: {process.returncode}")
                print("STDOUT:", stdout.decode('utf-8'))
                print("STDERR:", stderr.decode('utf-8'))
                return False
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            self.log_result(description, False, duration, str(e))
            return False
    
    def check_dependencies(self) -> bool:
        """检查依赖"""
        print("🔍 检查依赖...")
        
        required_packages = ['httpx', 'asyncio']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install httpx")
            return False
        
        print("✅ 依赖检查通过")
        return True
    
    def check_services(self) -> bool:
        """检查服务状态"""
        print("\n🔍 检查Docker服务状态...")
        
        try:
            # 检查docker compose状态
            result = subprocess.run(
                ['docker', 'compose', 'ps'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                if 'Up' in output:
                    print("✅ Docker服务正在运行")
                    return True
                else:
                    print("⚠️ 部分Docker服务可能未运行")
                    print(output)
                    return False
            else:
                print("❌ 无法获取Docker服务状态")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Docker命令超时")
            return False
        except FileNotFoundError:
            print("❌ Docker未安装或不在PATH中")
            return False
        except Exception as e:
            print(f"❌ 检查Docker服务时出错: {e}")
            return False
    
    async def run_all_tests(self, test_type: str = "all"):
        """运行所有测试"""
        print("🧪 知深学习导师 - 功能验证测试套件")
        print("=" * 60)
        
        # 检查前置条件
        if not self.check_dependencies():
            return False
        
        if not self.check_services():
            print("⚠️ 服务状态检查失败，但继续运行测试...")
        
        # 定义测试脚本
        tests = [
            ("quick_validation.py", "快速系统验证"),
            ("validate_user_stories.py", "用户故事验证"),
            ("e2e_learning_flow_test.py", "端到端学习流程测试"),
        ]
        
        # 根据测试类型过滤
        if test_type == "quick":
            tests = [tests[0]]  # 只运行快速验证
        elif test_type == "user_stories":
            tests = [tests[1]]  # 只运行用户故事验证
        elif test_type == "e2e":
            tests = [tests[2]]  # 只运行端到端测试
        
        # 运行测试
        total_tests = len(tests)
        passed_tests = 0
        
        for script_name, description in tests:
            success = await self.run_script(script_name, description)
            if success:
                passed_tests += 1
            
            # 测试间隔
            if script_name != tests[-1][0]:  # 不是最后一个测试
                print("\n⏳ 等待5秒后继续下一个测试...")
                await asyncio.sleep(5)
        
        # 输出总结
        self.print_final_summary(total_tests, passed_tests)
        
        return passed_tests == total_tests
    
    def print_final_summary(self, total_tests: int, passed_tests: int):
        """打印最终总结"""
        print("\n" + "=" * 60)
        print("📊 测试套件总结")
        print("=" * 60)
        
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {success_rate:.1f}%")
        
        # 详细结果
        print("\n📋 详细结果:")
        for result in self.results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test_name']} ({result['duration']:.1f}s)")
            if result["details"] and not result["success"]:
                print(f"    错误: {result['details']}")
        
        # 总体评估
        print("\n🎯 总体评估:")
        if failed_tests == 0:
            print("🎉 恭喜！所有测试都通过了！")
            print("✅ 知深学习导师系统功能完整，可以正常使用。")
        elif success_rate >= 80:
            print("👍 大部分测试通过，系统基本可用。")
            print("⚠️ 建议检查失败的测试项目。")
        elif success_rate >= 50:
            print("⚠️ 部分测试失败，系统可能存在问题。")
            print("🔧 建议检查服务配置和依赖。")
        else:
            print("❌ 多数测试失败，系统存在严重问题。")
            print("🚨 建议检查系统部署和配置。")
        
        # 建议
        print("\n💡 下一步建议:")
        if failed_tests == 0:
            print("- 系统已准备就绪，可以开始使用")
            print("- 可以进行用户验收测试")
            print("- 考虑部署到生产环境")
        else:
            print("- 检查失败的测试项目")
            print("- 查看服务日志: docker compose logs")
            print("- 重启服务: docker compose restart")
            print("- 检查配置文件和环境变量")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="知深学习导师功能验证测试套件")
    parser.add_argument(
        "--type", 
        choices=["all", "quick", "user_stories", "e2e"],
        default="all",
        help="测试类型 (默认: all)"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        success = asyncio.run(runner.run_all_tests(args.type))
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试运行器出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
