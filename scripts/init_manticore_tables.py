#!/usr/bin/env python3
"""
初始化Manticore搜索引擎表

创建必要的表结构以支持文档搜索和向量搜索功能
"""

import asyncio
import sys
import os

# 添加backend路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.services.search.manticore_service import ManticoreService


async def init_manticore_tables():
    """初始化Manticore表"""
    print("🔧 开始初始化Manticore搜索引擎表...")
    
    try:
        # 创建Manticore服务实例
        manticore_service = ManticoreService()
        
        # 创建documents表
        print("📄 创建documents表...")
        success = await manticore_service.create_documents_table("documents")
        
        if success:
            print("✅ documents表创建成功")
        else:
            print("❌ documents表创建失败")
            return False
        
        # 测试表是否可用
        print("🧪 测试表功能...")
        
        # 尝试插入一个测试文档
        test_doc_id = 999999
        test_title = "测试文档"
        test_content = "这是一个测试文档，用于验证Manticore搜索功能是否正常工作。"
        test_source = "test"
        test_embedding = [0.1] * 1536  # 1536维测试向量
        
        index_success = await manticore_service.index_document(
            table_name="documents",
            doc_id=test_doc_id,
            title=test_title,
            content=test_content,
            source=test_source,
            embedding=test_embedding
        )
        
        if index_success:
            print("✅ 测试文档索引成功")
            
            # 测试搜索功能
            search_results = await manticore_service.search_documents(
                table_name="documents",
                query="测试",
                limit=1
            )
            
            if search_results and search_results.get("hits"):
                print("✅ 文本搜索功能正常")
            else:
                print("⚠️ 文本搜索功能可能有问题")
            
            # 测试向量搜索
            vector_results = await manticore_service.vector_search(
                table_name="documents",
                query_embedding=test_embedding,
                limit=1
            )
            
            if vector_results and vector_results.get("hits"):
                print("✅ 向量搜索功能正常")
            else:
                print("⚠️ 向量搜索功能可能有问题")
            
            # 清理测试文档
            await manticore_service.delete_document("documents", test_doc_id)
            print("🧹 测试文档已清理")
            
        else:
            print("❌ 测试文档索引失败")
            return False
        
        print("\n🎉 Manticore搜索引擎初始化完成！")
        print("📊 表结构:")
        print("  - documents: 文档搜索表，支持全文搜索和向量搜索")
        print("  - 字段: id, title, content, source, embedding")
        print("  - 索引: 全文索引 + 向量索引")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def check_manticore_status():
    """检查Manticore服务状态"""
    print("🔍 检查Manticore服务状态...")
    
    try:
        manticore_service = ManticoreService()
        
        # 检查连接
        async with manticore_service.client as client:
            # 尝试执行一个简单的查询
            result = await client.execute_sql("SHOW TABLES")
            print(f"✅ Manticore连接正常，当前表: {result}")
            return True
            
    except Exception as e:
        print(f"❌ Manticore连接失败: {str(e)}")
        return False


async def main():
    """主函数"""
    print("🚀 Manticore搜索引擎初始化工具")
    print("=" * 50)
    
    # 检查服务状态
    if not await check_manticore_status():
        print("\n💡 故障排除建议:")
        print("1. 确保Manticore服务正在运行: docker compose ps")
        print("2. 检查Manticore服务日志: docker compose logs manticore")
        print("3. 确保端口9308可访问")
        return False
    
    # 初始化表
    success = await init_manticore_tables()
    
    if success:
        print("\n✅ 初始化成功！现在可以运行对话功能测试了。")
        return True
    else:
        print("\n❌ 初始化失败！请检查错误信息并重试。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
