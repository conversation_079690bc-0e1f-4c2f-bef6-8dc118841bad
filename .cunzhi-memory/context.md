# 项目上下文信息

- Master-Know 端到端测试进展总结：

**✅ 已解决的关键问题：**
1. Docker 构建上下文问题：重新构建 backend 镜像，正确包含了项目根目录的 engines 模块
2. Backend API 配置问题：修复了 backend/app/core/config.py 中 MANTICORE_PORT 配置不匹配（代码期望 MANTICORE_PORT，但配置只有 MANTICORE_HTTP_PORT）
3. 用户认证流程：确认可以使用默认超级用户 <EMAIL>/changethis 正常登录获取 JWT token
4. 文档创建 API：确认正确端点是 POST /api/v1/documents/（注意末尾斜杠），使用 JSON 格式而非文件上传

**🔄 当前系统状态：**
- 所有 Docker 服务正常运行：backend, gateway, db, redis, manticore, dramatiq-worker, frontend, adminer
- Backend 服务在容器内正常工作（docker exec 测试通过）
- 但从主机访问 localhost:8000 时出现间歇性网络超时问题

**❌ 待解决的问题：**
1. Docker 网络连接不稳定：Backend API 从主机访问时经常超时，但容器内部正常
2. 基础服务集成测试失败：Manticore HTTP 返回 501，Redis/PostgreSQL 库未安装，engines 模块依赖缺失
3. 文档处理流程未完整测试：文档创建后的切片、向量化、索引等步骤

**📋 测试脚本状态：**
- 创建了完整的集成测试脚本 test_system_integration.py
- 端到端测试在网络稳定时可以通过用户认证和健康检查
- 需要解决网络连接问题后继续测试文档处理流程
