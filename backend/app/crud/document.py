"""
文档相关 CRUD 操作
包含文档和文档块的创建、更新、查询、删除等操作
"""
import uuid
from datetime import datetime
from typing import Any

from sqlmodel import Session, select

from app.models import (
    Document,
    DocumentCreate,
    DocumentUpdate,
    DocumentChunk,
    DocumentChunkCreate,
    DocumentChunkUpdate,
)
from .base import CRUDBase, get_by_owner, count_by_owner


class CRUDDocument(CRUDBase[Document, DocumentCreate, DocumentUpdate]):
    """文档 CRUD 操作类"""
    
    def create_with_owner(
        self, session: Session, *, obj_in: DocumentCreate, owner_id: uuid.UUID
    ) -> Document:
        """创建文档（指定所有者）"""
        obj_in_data = obj_in.model_dump()
        db_obj = Document(**obj_in_data, owner_id=owner_id)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def get_multi_by_owner(
        self,
        session: Session,
        *,
        owner_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[Document]:
        """根据所有者获取文档列表"""
        return get_by_owner(
            session, model=Document, owner_id=owner_id, skip=skip, limit=limit
        )
    
    def count_by_owner(self, session: Session, *, owner_id: uuid.UUID) -> int:
        """统计所有者的文档数量"""
        return count_by_owner(session, model=Document, owner_id=owner_id)
    
    def update_timestamp(self, session: Session, *, db_obj: Document) -> Document:
        """更新文档的修改时间"""
        db_obj.updated_at = datetime.utcnow()
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj


class CRUDDocumentChunk(CRUDBase[DocumentChunk, DocumentChunkCreate, DocumentChunkUpdate]):
    """文档块 CRUD 操作类"""
    
    def get_by_document(
        self,
        session: Session,
        *,
        document_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[DocumentChunk]:
        """根据文档 ID 获取文档块列表"""
        statement = (
            select(DocumentChunk)
            .where(DocumentChunk.document_id == document_id)
            .order_by(DocumentChunk.chunk_index)
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def count_by_document(self, session: Session, *, document_id: uuid.UUID) -> int:
        """统计文档的块数量"""
        statement = select(DocumentChunk).where(DocumentChunk.document_id == document_id)
        return len(session.exec(statement).all())
    
    def delete_by_document(self, session: Session, *, document_id: uuid.UUID) -> int:
        """删除文档的所有块"""
        statement = select(DocumentChunk).where(DocumentChunk.document_id == document_id)
        chunks = session.exec(statement).all()
        count = len(chunks)
        for chunk in chunks:
            session.delete(chunk)
        session.commit()
        return count


# 创建 CRUD 实例
document = CRUDDocument(Document)
document_chunk = CRUDDocumentChunk(DocumentChunk)


# 文档相关的兼容性函数
def create_document(*, session: Session, document_in: DocumentCreate, owner_id: uuid.UUID) -> Document:
    """创建文档"""
    return document.create_with_owner(session, obj_in=document_in, owner_id=owner_id)


def get_document(*, session: Session, document_id: uuid.UUID) -> Document | None:
    """获取文档"""
    return document.get(session, id=document_id)


def get_documents_by_owner(
    session: Session, *, owner_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[Document]:
    """根据所有者获取文档列表"""
    return document.get_multi_by_owner(session, owner_id=owner_id, skip=skip, limit=limit)


def update_document(*, session: Session, db_document: Document, document_in: DocumentUpdate) -> Document:
    """更新文档"""
    updated_doc = document.update(session, db_obj=db_document, obj_in=document_in)
    return document.update_timestamp(session, db_obj=updated_doc)


def delete_document(*, session: Session, document_id: uuid.UUID) -> Document | None:
    """删除文档"""
    return document.remove(session, id=document_id)


# 文档块相关的兼容性函数
def create_document_chunk(*, session: Session, chunk_in: DocumentChunkCreate) -> DocumentChunk:
    """创建文档块"""
    return document_chunk.create(session, obj_in=chunk_in)


def get_document_chunks(
    session: Session, *, document_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[DocumentChunk]:
    """获取文档块列表"""
    return document_chunk.get_by_document(session, document_id=document_id, skip=skip, limit=limit)


def delete_document_chunks(*, session: Session, document_id: uuid.UUID) -> int:
    """删除文档的所有块"""
    return document_chunk.delete_by_document(session, document_id=document_id)
