"""
主题管理 API 路由

基于独立主题服务的实现，集成到主后端API中
"""

import uuid
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from app.api.deps import CurrentUser, SessionDep
from app.models.topic import Topic, TopicCreate, TopicUpdate, TopicPublic, TopicsPublic
from app.crud.topic import create_topic, get_topic, get_topics_by_owner, update_topic, delete_topic

router = APIRouter(prefix="/topics", tags=["topics"])


class TopicStatsResponse(BaseModel):
    """主题统计响应模型"""
    topic_id: uuid.UUID
    document_count: int = 0
    conversation_count: int = 0
    last_activity: Optional[str] = None


class UserTopicStatsResponse(BaseModel):
    """用户主题统计响应模型"""
    user_id: uuid.UUID
    total_topics: int = 0
    active_topics: int = 0
    total_documents: int = 0
    total_conversations: int = 0


@router.post("/", response_model=TopicPublic)
def create_new_topic(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    topic_in: TopicCreate,
) -> Any:
    """
    创建新主题
    """
    try:
        topic = create_topic(
            session=session,
            topic_in=topic_in,
            owner_id=current_user.id
        )
        return topic
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/", response_model=TopicsPublic)
def read_topics(
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
) -> Any:
    """
    获取当前用户的主题列表
    """
    topics = get_topics_by_owner(
        session=session,
        owner_id=current_user.id,
        skip=skip,
        limit=limit
    )
    
    return TopicsPublic(data=topics, count=len(topics))


@router.get("/{topic_id}", response_model=TopicPublic)
def read_topic(
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
) -> Any:
    """
    获取特定主题的详细信息
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此主题")
    
    return topic


@router.put("/{topic_id}", response_model=TopicPublic)
def update_existing_topic(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
    topic_in: TopicUpdate,
) -> Any:
    """
    更新主题信息
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此主题")
    
    topic = update_topic(
        session=session,
        db_topic=topic,
        topic_in=topic_in
    )
    
    return topic


@router.delete("/{topic_id}")
def delete_existing_topic(
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
) -> Any:
    """
    删除主题
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此主题")
    
    delete_topic(session=session, topic_id=topic_id)
    
    return {"message": "主题删除成功"}


@router.get("/{topic_id}/stats", response_model=TopicStatsResponse)
def get_topic_statistics(
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
) -> Any:
    """
    获取主题统计信息
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此主题")
    
    # TODO: 实现实际的统计逻辑
    # 这里可以查询关联的文档数量、对话数量等
    stats = TopicStatsResponse(
        topic_id=topic_id,
        document_count=0,  # 从数据库查询
        conversation_count=0,  # 从数据库查询
        last_activity=topic.updated_at.isoformat() if topic.updated_at else None
    )
    
    return stats


@router.get("/users/me/stats", response_model=UserTopicStatsResponse)
def get_user_topic_statistics(
    session: SessionDep,
    current_user: CurrentUser,
) -> Any:
    """
    获取当前用户的主题统计信息
    """
    topics = get_topics_by_owner(
        session=session,
        owner_id=current_user.id,
        skip=0,
        limit=1000  # 获取所有主题用于统计
    )
    
    # TODO: 实现更详细的统计逻辑
    stats = UserTopicStatsResponse(
        user_id=current_user.id,
        total_topics=len(topics),
        active_topics=len(topics),  # 简化实现，实际应该查询有活动的主题
        total_documents=0,  # 从数据库查询
        total_conversations=0  # 从数据库查询
    )
    
    return stats


# 文档关联相关的端点可以在后续添加
# 这些端点需要与文档服务集成

@router.post("/{topic_id}/documents/{document_id}")
def attach_document_to_topic(
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
    document_id: uuid.UUID,
) -> Any:
    """
    将文档关联到主题
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此主题")
    
    # TODO: 实现文档关联逻辑
    # 这需要与文档服务集成，验证文档存在并创建关联关系
    
    return {"message": "文档关联成功"}


@router.delete("/{topic_id}/documents/{document_id}")
def detach_document_from_topic(
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
    document_id: uuid.UUID,
) -> Any:
    """
    取消文档与主题的关联
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此主题")
    
    # TODO: 实现取消文档关联逻辑
    
    return {"message": "文档关联已取消"}


@router.get("/{topic_id}/documents")
def get_topic_documents(
    session: SessionDep,
    current_user: CurrentUser,
    topic_id: uuid.UUID,
) -> Any:
    """
    获取主题关联的文档列表
    """
    topic = get_topic(session=session, topic_id=topic_id)
    
    if not topic:
        raise HTTPException(status_code=404, detail="主题不存在")
    
    # 检查权限
    if not current_user.is_superuser and topic.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此主题")
    
    # TODO: 实现获取关联文档逻辑
    
    return {
        "topic_id": topic_id,
        "documents": [],  # 从数据库查询
        "total": 0
    }
