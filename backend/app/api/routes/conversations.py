"""
对话管理 API 路由

提供智能对话、上下文管理、摘要生成等核心功能
"""

import uuid
import json
from datetime import datetime
from typing import Any, List, Optional

from fastapi import APIRouter, HTTPException, Query, BackgroundTasks, WebSocket, WebSocketDisconnect
from pydantic import BaseModel

from app.api.deps import CurrentUser, SessionDep
from app.models.conversation import (
    Conversation, ConversationCreate, ConversationUpdate, ConversationPublic, ConversationsPublic,
    ConversationMessage, ConversationMessageCreate, ConversationMessagePublic, ConversationMessagesPublic,
    MessageRole, ConversationStatus
)
from app.crud.conversation import (
    create_conversation, get_conversation, get_conversations_by_owner, update_conversation,
    create_conversation_message, get_conversation_messages
)
from app.services.llm import get_openai_provider, build_prompt
from app.services.search.search_manager import get_search_manager

router = APIRouter(prefix="/conversations", tags=["conversations"])


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str
    conversation_id: Optional[uuid.UUID] = None
    topic_id: Optional[uuid.UUID] = None
    max_tokens: int = 2048
    temperature: float = 0.7
    use_context: bool = True


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str
    conversation_id: uuid.UUID
    message_id: uuid.UUID
    context_used: List[dict] = []
    summary_generated: bool = False


class ConversationSummaryRequest(BaseModel):
    """对话摘要请求模型"""
    conversation_id: uuid.UUID
    force_regenerate: bool = False


class ConversationSummaryResponse(BaseModel):
    """对话摘要响应模型"""
    conversation_id: uuid.UUID
    summary: dict
    generated_at: str


@router.post("/", response_model=ConversationPublic)
def create_new_conversation(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_in: ConversationCreate,
) -> Any:
    """
    创建新对话
    """
    try:
        conversation = create_conversation(
            session=session,
            conversation_in=conversation_in,
            owner_id=current_user.id
        )
        return conversation
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/", response_model=ConversationsPublic)
def read_conversations(
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
) -> Any:
    """
    获取当前用户的对话列表
    """
    conversations = get_conversations_by_owner(
        session=session,
        owner_id=current_user.id,
        skip=skip,
        limit=limit
    )
    
    return ConversationsPublic(data=conversations, count=len(conversations))


@router.get("/{conversation_id}", response_model=ConversationPublic)
def read_conversation(
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
) -> Any:
    """
    获取特定对话的详细信息
    """
    conversation = get_conversation(session=session, conversation_id=conversation_id)
    
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    
    # 检查权限
    if not current_user.is_superuser and conversation.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此对话")
    
    return conversation


@router.get("/{conversation_id}/messages", response_model=ConversationMessagesPublic)
def read_conversation_messages(
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="返回的记录数"),
) -> Any:
    """
    获取对话的消息列表
    """
    conversation = get_conversation(session=session, conversation_id=conversation_id)
    
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    
    # 检查权限
    if not current_user.is_superuser and conversation.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此对话")
    
    messages = get_conversation_messages(
        session=session,
        conversation_id=conversation_id,
        skip=skip,
        limit=limit
    )
    
    return ConversationMessagesPublic(data=messages, count=len(messages))


@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    background_tasks: BackgroundTasks,
    request: ChatRequest,
) -> Any:
    """
    与AI进行智能对话
    
    核心功能：
    1. 智能上下文检索和拼接
    2. 多轮对话管理
    3. 异步摘要生成
    """
    try:
        # 1. 获取或创建对话
        conversation = None
        if request.conversation_id:
            conversation = get_conversation(session=session, conversation_id=request.conversation_id)
            if not conversation or conversation.owner_id != current_user.id:
                raise HTTPException(status_code=404, detail="对话不存在或无权限访问")
        else:
            # 创建新对话
            conversation_data = ConversationCreate(
                title=f"对话 - {request.message[:30]}...",
                topic_id=request.topic_id
            )
            conversation = create_conversation(
                session=session,
                conversation_in=conversation_data,
                owner_id=current_user.id
            )
        
        # 2. 保存用户消息
        user_message_data = ConversationMessageCreate(
            conversation_id=conversation.id,
            role=MessageRole.USER,
            content=request.message
        )
        user_message = create_conversation_message(session=session, message_in=user_message_data)
        
        # 3. 智能上下文检索
        context_results = []
        if request.use_context:
            context_results = await retrieve_smart_context(
                query=request.message,
                conversation_id=conversation.id,
                topic_id=conversation.topic_id,
                user_id=current_user.id,
                session=session
            )
        
        # 4. 构建对话历史
        recent_messages = get_conversation_messages(
            session=session,
            conversation_id=conversation.id,
            skip=0,
            limit=10  # 最近10条消息
        )
        
        # 转换为LLM格式
        history = []
        for msg in recent_messages[:-1]:  # 排除刚添加的用户消息
            history.append({
                "role": msg.role.value,
                "content": msg.content
            })
        
        # 5. 构建上下文字符串
        context_text = ""
        if context_results:
            context_text = "\n".join([
                f"相关内容 {i+1}: {result['content']}"
                for i, result in enumerate(context_results[:3])  # 最多3个上下文
            ])
        
        # 6. 生成AI回复
        llm_provider = get_openai_provider()
        system_prompt = """你是一个智能学习导师。基于提供的上下文信息，为用户提供准确、有帮助的回答。
如果上下文信息不足，请诚实地说明，并提供你能给出的最佳建议。
保持回答简洁明了，重点突出。"""
        
        messages = build_prompt(
            system_prompt=system_prompt,
            context=context_text,
            history=history,
            user_query=request.message
        )
        
        ai_response = await llm_provider.generate(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )
        
        # 7. 保存AI回复
        ai_message_data = ConversationMessageCreate(
            conversation_id=conversation.id,
            role=MessageRole.ASSISTANT,
            content=ai_response["text"]
        )
        ai_message = create_conversation_message(session=session, message_in=ai_message_data)
        
        # 8. 异步生成摘要（如果对话达到一定长度）
        message_count = len(recent_messages) + 1  # 包括新的AI消息
        should_generate_summary = message_count >= 4 and message_count % 4 == 0  # 每4条消息生成一次摘要
        
        if should_generate_summary:
            background_tasks.add_task(
                generate_conversation_summary,
                conversation.id,
                session
            )
        
        return ChatResponse(
            message=ai_response["text"],
            conversation_id=conversation.id,
            message_id=ai_message.id,
            context_used=context_results,
            summary_generated=should_generate_summary
        )
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"对话处理错误: {error_details}")
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")


async def retrieve_smart_context(
    query: str,
    conversation_id: uuid.UUID,
    topic_id: Optional[uuid.UUID],
    user_id: uuid.UUID,
    session: SessionDep,
    limit: int = 5
) -> List[dict]:
    """
    智能上下文检索
    
    结合文档搜索、对话历史、主题相关性进行智能上下文拼接
    """
    context_results = []
    
    try:
        # 1. 文档语义搜索
        search_manager = get_search_manager()
        doc_results = await search_manager.semantic_search(
            query=query,
            limit=3,
            similarity_threshold=0.7
        )
        
        if doc_results.get("hits"):
            for hit in doc_results["hits"]:
                context_results.append({
                    "type": "document",
                    "content": hit.get("content", ""),
                    "source": hit.get("source", ""),
                    "score": hit.get("score", 0.0)
                })
        
        # 2. 对话历史相关性搜索
        # TODO: 实现基于向量的对话历史搜索
        
        # 3. 主题相关内容
        # TODO: 如果有topic_id，搜索主题相关的知识点
        
    except Exception as e:
        print(f"上下文检索失败: {e}")
    
    return context_results


async def generate_conversation_summary(conversation_id: uuid.UUID, session):
    """
    异步生成对话摘要
    
    生成结构化摘要：{"user_summary": "...", "assistant_summary": "..."}
    """
    try:
        # 获取最近的消息
        messages = get_conversation_messages(
            session=session,
            conversation_id=conversation_id,
            skip=0,
            limit=10
        )
        
        if len(messages) < 2:
            return
        
        # 构建摘要prompt
        conversation_text = ""
        for msg in messages[-4:]:  # 最近4条消息
            role_name = "用户" if msg.role == MessageRole.USER else "AI导师"
            conversation_text += f"{role_name}: {msg.content}\n"
        
        llm_provider = get_openai_provider()
        summary_prompt = f"""请为以下对话生成结构化摘要，格式为JSON：
{{"user_summary": "用户在这轮对话中的核心观点或问题", "assistant_summary": "AI导师的核心回应和要点"}}

对话内容：
{conversation_text}

请确保摘要简洁明了，突出关键信息。"""
        
        messages = [{"role": "user", "content": summary_prompt}]
        
        result = await llm_provider.generate(
            messages=messages,
            max_tokens=500,
            temperature=0.3
        )
        
        # TODO: 将摘要保存到数据库
        # 这里需要创建摘要数据模型和存储逻辑
        print(f"生成对话摘要: {result['text']}")
        
    except Exception as e:
        print(f"摘要生成失败: {e}")


@router.post("/{conversation_id}/summary", response_model=ConversationSummaryResponse)
async def generate_summary(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    force_regenerate: bool = False,
) -> Any:
    """
    手动生成对话摘要
    """
    conversation = get_conversation(session=session, conversation_id=conversation_id)
    
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    
    # 检查权限
    if not current_user.is_superuser and conversation.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限访问此对话")
    
    try:
        # 获取对话消息
        messages = get_conversation_messages(
            session=session,
            conversation_id=conversation_id,
            skip=0,
            limit=10
        )

        if len(messages) < 2:
            raise HTTPException(status_code=400, detail="对话消息不足，无法生成摘要")

        # 构建摘要prompt
        conversation_text = ""
        for msg in messages[-4:]:  # 最近4条消息
            role_name = "用户" if msg.role == MessageRole.USER else "AI导师"
            conversation_text += f"{role_name}: {msg.content}\n"

        llm_provider = get_openai_provider()
        summary_prompt = f"""请为以下对话生成结构化摘要，格式为JSON：
{{"user_summary": "用户在这轮对话中的核心观点或问题", "assistant_summary": "AI导师的核心回应和要点"}}

对话内容：
{conversation_text}

请确保摘要简洁明了，突出关键信息。只返回JSON格式的摘要，不要包含其他文本。"""

        messages_for_llm = [{"role": "user", "content": summary_prompt}]

        result = await llm_provider.generate(
            messages=messages_for_llm,
            max_tokens=500,
            temperature=0.3
        )

        # 尝试解析JSON摘要
        try:
            import json
            summary = json.loads(result['text'])
        except json.JSONDecodeError:
            # 如果解析失败，使用默认格式
            summary = {
                "user_summary": "用户询问了相关问题",
                "assistant_summary": result['text'][:200] + "..."
            }

        return ConversationSummaryResponse(
            conversation_id=conversation_id,
            summary=summary,
            generated_at=datetime.utcnow().isoformat() + "Z"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"摘要生成失败: {str(e)}")


@router.put("/{conversation_id}/status")
def update_conversation_status(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    status: ConversationStatus,
) -> Any:
    """
    更新对话状态
    """
    conversation = get_conversation(session=session, conversation_id=conversation_id)
    
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    
    # 检查权限
    if not current_user.is_superuser and conversation.owner_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此对话")
    
    conversation_update = ConversationUpdate(status=status)
    updated_conversation = update_conversation(
        session=session,
        db_conversation=conversation,
        conversation_in=conversation_update
    )
    
    return {"message": "对话状态更新成功", "status": updated_conversation.status}


@router.websocket("/ws/{conversation_id}")
async def websocket_chat(websocket: WebSocket, conversation_id: uuid.UUID):
    """
    WebSocket端点，支持实时对话

    提供流式AI回复和实时通信功能
    """
    await websocket.accept()

    try:
        # 发送连接确认
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "conversation_id": str(conversation_id),
            "message": "WebSocket连接已建立"
        }))

        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # 验证消息格式
            if "message" not in message_data:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "消息格式错误：缺少message字段"
                }))
                continue

            # 发送处理中状态
            await websocket.send_text(json.dumps({
                "type": "processing",
                "message": "正在处理您的消息..."
            }))

            try:
                # TODO: 这里需要集成实际的对话处理逻辑
                # 目前返回模拟响应
                response_message = f"收到您的消息：{message_data['message']}"

                # 发送AI回复
                await websocket.send_text(json.dumps({
                    "type": "ai_response",
                    "conversation_id": str(conversation_id),
                    "message": response_message,
                    "timestamp": "2025-08-15T06:00:00Z"
                }))

            except Exception as e:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"处理消息时出错：{str(e)}"
                }))

    except WebSocketDisconnect:
        print(f"WebSocket连接断开：对话 {conversation_id}")
    except Exception as e:
        print(f"WebSocket错误：{str(e)}")
        await websocket.close(code=1011, reason=str(e))
