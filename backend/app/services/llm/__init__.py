from typing import List, Optional
from pydantic import BaseModel, Field
import httpx
import openai
from app.core.config import settings

class Message(BaseModel):
    role: str = Field(description="Message role: system, user, or assistant")
    content: str = Field(description="Message content")

class GenerateRequest(BaseModel):
    conversation_id: str = Field(description="Conversation ID for context retrieval")
    user_query: str = Field(description="User query to process")
    history: Optional[List[Message]] = Field(default=[], description="Conversation history")
    max_tokens: Optional[int] = Field(default=4096, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(default=0.2, description="Generation temperature")

class GenerateResponse(BaseModel):
    text: str = Field(description="Generated response")
    usage: dict = Field(description="Token usage information")
    metadata: dict = Field(description="Additional metadata")

class OpenAIProvider:
    def __init__(self, api_key: str, base_url: str = None, model: str = "gpt-4o-mini"):
        client_kwargs = {"api_key": api_key}
        if base_url:
            client_kwargs["base_url"] = base_url
        
        self.client = openai.AsyncOpenAI(**client_kwargs)
        self.model = model
    
    async def generate(self, messages: List[dict], max_tokens: int = 4096, temperature: float = 0.2) -> dict:
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return {
                "text": response.choices[0].message.content,
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                },
                "model": response.model,
                "finish_reason": response.choices[0].finish_reason
            }
        except Exception as e:
            from fastapi import HTTPException
            raise HTTPException(status_code=500, detail=f"OpenAI API error: {str(e)}")

# 初始化 provider
def get_openai_provider() -> OpenAIProvider:
    return OpenAIProvider(
        api_key=settings.LLM_OPENAI_API_KEY,
        base_url=settings.LLM_OPENAI_BASE_URL,
        model=settings.LLM_OPENAI_MODEL
    )

async def fetch_long_term_memory(conversation_id: str) -> str:
    """从 Manticore 获取长期记忆"""
    # TODO: 实际实现中会调用 Manticore 异步客户端
    return f"Long-term memory context for conversation {conversation_id}"

def build_prompt(system_prompt: str, context: str, history: List[dict], user_query: str) -> List[dict]:
    """构建完整的对话 prompt"""
    messages = []

    # 系统提示
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})

    # 长期记忆上下文
    if context:
        messages.append({"role": "system", "content": f"Context: {context}"})

    # 对话历史
    for msg in history:
        # 支持字典格式的历史消息
        if isinstance(msg, dict):
            messages.append({"role": msg["role"], "content": msg["content"]})
        else:
            # 支持Message对象格式
            messages.append({"role": msg.role, "content": msg.content})

    # 当前用户查询
    messages.append({"role": "user", "content": user_query})

    return messages